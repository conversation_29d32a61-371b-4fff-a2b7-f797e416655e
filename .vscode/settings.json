{"eslint.useFlatConfig": true, "prettier.enable": false, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.organizeImports": "never"}, "stylelint.validate": ["css", "postcss", "scss", "vue"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "markdown", "json", "jsonc", "yaml"], "typescript.tsdk": "node_modules/typescript/lib"}