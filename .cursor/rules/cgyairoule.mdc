# 系统管理模块文档

## 概述

系统管理模块是基于 Fantastic-admin 框架开发的权限管理系统，包含菜单管理、角色管理、账号管理等功能。该模块已对接后端 API 接口，支持完整的 CRUD 操作。

## 重要提示：自动导入功能

**本项目配置了 `unplugin-auto-import` 插件，实现了 Vue3 常用 API 的自动导入功能。**

### 自动导入的内容

项目在 `src/types/auto-imports.d.ts` 中自动导入了以下内容：

1. **Vue 3 核心 API**：
   - `ref`, `reactive`, `computed`, `watch`, `watchEffect`
   - `nextTick`, `onMounted`, `onUnmounted`
   - `defineComponent`, `defineAsyncComponent`
   - 等等...

2. **Vue Router API**：
   - `useRoute`, `useRouter`
   - `onBeforeRouteLeave`, `onBeforeRouteUpdate`

3. **Pinia API**：
   - `defineStore`, `storeToRefs`
   - `useUserStore`, `useMenuStore`, `useSettingsStore` 等

4. **项目自定义 Composables**：
   - `useAuth`, `useMenu`, `useTabbar`
   - `useGlobalProperties`, `useMainPage`

### 开发注意事项

1. **无需手动导入**：上述 API 可以直接使用，无需在 `<script setup>` 中手动导入
2. **TypeScript 支持**：自动导入的 API 具有完整的类型支持
3. **避免重复导入**：如果手动导入了已自动导入的 API，可能会导致 TypeScript 检查器混淆

### 正确的写法示例

```vue
<script setup lang="ts">
// ✅ 正确：直接使用自动导入的 API
const loading = ref(false)
const userStore = useUserStore()

onMounted(() => {
  // 初始化逻辑
})

// ❌ 错误：不要手动导入已自动导入的 API
// import { ref, onMounted } from 'vue'
// import { useUserStore } from '@/store/modules/user'
</script>
```

### 为什么某些组件没有 TypeScript 错误

如果您发现某些组件（如 `LoginForm.vue`）没有 TypeScript 错误，而其他组件有错误，通常是因为：

1. **正确使用了自动导入**：组件直接使用了自动导入的 API，没有手动导入冲突
2. **手动导入冲突**：某些组件手动导入了已自动导入的 API，导致 TypeScript 检查器混淆
3. **解决方案**：移除手动导入，直接使用自动导入的 API

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **UI 组件**: Fantastic-admin UI 组件库
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP 客户端**: Axios
- **权限控制**: 基于路由和按钮级别的权限验证
- **自动导入**: unplugin-auto-import 插件

## 目录结构

```
src/
├── api/modules/
│   └── system.ts              # 系统管理 API 接口
├── views/system/
│   └── menu.vue              # 菜单管理页面
├── router/modules/
│   └── system.ts             # 系统管理路由配置
└── menu/modules/
    └── system.ts             # 系统管理菜单配置
```

## API 接口文档

### 基础配置

所有 API 接口都配置在 `src/api/modules/system.ts` 中，统一使用 POST 方法，Content-Type 为 `application/json`。

### 菜单管理接口

#### 1. 获取菜单列表

```typescript
getMenuList: (params: {
  page?: number      // 页码，从 0 开始
  size?: number      // 每页数量
  parentId?: string  // 父级菜单 ID，为空表示获取一级菜单
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_menu/list`

**权限要求**: `/system/menu/query`

**返回数据**:
```json
{
  "code": 0,
  "data": {
    "total": 10,
    "content": [
      {
        "id": "menu_id",
        "fullPath": "/system/menu",
        "path": "menu",
        "name": "菜单管理",
        "parentId": "parent_id",
        "ctime": 1752575927973
      }
    ]
  }
}
```

#### 2. 编辑菜单

```typescript
editMenu: (data: {
  id: string    // 菜单 ID
  name: string  // 菜单名称
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_menu/edit`

**权限要求**: `/system/menu/save`

#### 3. 删除菜单

```typescript
deleteMenu: (data: {
  id: string  // 菜单 ID
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_menu/delete`

**权限要求**: `/system/menu/delete`

### 角色管理接口

#### 1. 获取角色列表

```typescript
getRoleList: (params: {
  page?: number  // 页码
  size?: number  // 每页数量
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_role/list`

**权限要求**: `/system/role/query`

#### 2. 获取角色详情

```typescript
getRoleDetail: (data: {
  id: string  // 角色 ID
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_role/getDetailById`

**权限要求**: `/system/role/query`

#### 3. 新增/编辑角色

```typescript
saveRole: (data: {
  id?: string      // 角色 ID，编辑时提供
  name: string     // 角色名称
  menuIds?: string[] // 菜单权限 ID 数组
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_role/save`

**权限要求**: `/system/role/save`

#### 4. 删除角色

```typescript
deleteRole: (data: {
  id: string  // 角色 ID
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_role/delete`

**权限要求**: `/system/role/delete`

### 账号管理接口

#### 1. 获取账号列表

```typescript
getAccountList: (params: {
  page?: number  // 页码
  size?: number  // 每页数量
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_account/list`

**权限要求**: `/system/account/query`

#### 2. 新增/编辑账号

```typescript
saveAccount: (data: {
  id?: string      // 账号 ID，编辑时提供
  name: string     // 账号名称
  account: string  // 登录账号
  roleIds?: string[] // 角色 ID 数组
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_account/save`

**权限要求**: `/system/account/save`

#### 3. 删除账号

```typescript
deleteAccount: (data: {
  id: string  // 账号 ID
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_account/delete`

**权限要求**: `/system/account/delete`

#### 4. 重置账号密码

```typescript
resetAccountPassword: (data: {
  id: string      // 账号 ID
  password: string // 新密码（需要 MD5 加密）
}) => Promise<any>
```

**接口地址**: `POST /zfilm/api/adm_account/resetPassword`

**权限要求**: `/system/account/resetPassword`

## 页面功能

### 菜单管理页面 (`/system/menu`)

#### 功能特性

1. **菜单列表展示**
   - 支持分页显示
   - 显示菜单名称、路径、全路径、创建时间
   - 支持按父级菜单筛选

2. **层级导航**
   - 支持查看子菜单
   - 面包屑导航显示当前位置
   - 返回上级功能

3. **编辑功能**
   - 弹窗编辑菜单名称
   - 表单验证
   - 操作成功/失败提示

4. **删除功能**
   - 确认对话框删除
   - 权限控制显示删除按钮

5. **权限控制**
   - 页面访问权限：`/system/menu/query`
   - 编辑权限：`/system/menu/save`
   - 删除权限：`/system/menu/delete`

#### 页面组件

```vue
<template>
  <div>
    <FaPageHeader title="菜单管理" description="管理系统菜单权限">
      <!-- 返回上级按钮 -->
    </FaPageHeader>

    <FaPageMain>
      <FaCard>
        <!-- 菜单列表表格 -->
        <!-- 分页组件 -->
      </FaCard>
    </FaPageMain>

    <!-- 编辑对话框 -->
    <!-- 删除确认对话框 -->
  </div>
</template>
```

## 路由配置

### 路由结构

```typescript
// src/router/modules/system.ts
const routes: RouteRecordRaw = {
  path: '/system',
  component: Layout,
  name: 'system',
  meta: {
    title: '系统管理',
    icon: 'i-ri:settings-3-line',
  },
  children: [
    {
      path: 'menu',
      name: 'systemMenu',
      component: () => import('@/views/system/menu.vue'),
      meta: {
        title: '菜单管理',
        icon: 'i-ri:menu-line',
        auth: '/system/menu/query',
      },
    },
  ],
}
```

### 菜单配置

```typescript
// src/menu/modules/system.ts
const menus: Menu.recordRaw = {
  meta: {
    title: '系统管理',
    icon: 'i-ri:settings-3-line',
  },
  children: [
    {
      path: '/system/menu',
      meta: {
        title: '菜单管理',
        icon: 'i-ri:menu-line',
        auth: '/system/menu/query',
      },
    },
  ],
}
```

## 权限系统

### 权限验证机制

1. **路由级权限**: 通过 `meta.auth` 配置页面访问权限
2. **按钮级权限**: 通过 `userStore.permissions` 判断用户权限
3. **API 级权限**: 后端接口权限验证

### 权限数据结构

```typescript
// 用户权限数组
permissions: string[] = [
  "/",
  "/system",
  "/system/menu",
  "/system/menu/query",
  "/system/menu/save",
  "/system/menu/delete",
  // ...
]
```

### 权限判断方法

```typescript
// 判断是否有某个权限
function hasPermission(permission: string): boolean {
  return userStore.permissions.includes(permission)
}

// 在模板中使用
<FaButton v-if="userStore.permissions.includes('/system/menu/save')">
  编辑
</FaButton>
```

## 开发指南

### 添加新的系统管理页面

1. **创建页面组件**
   ```bash
   # 在 src/views/system/ 下创建新页面
   touch src/views/system/role.vue
   ```

2. **添加 API 接口**
   ```typescript
   // 在 src/api/modules/system.ts 中添加接口方法
   getRoleList: (params) => api.post('zfilm/api/adm_role/list', params)
   ```

3. **配置路由**
   ```typescript
   // 在 src/router/modules/system.ts 中添加路由
   {
     path: 'role',
     name: 'systemRole',
     component: () => import('@/views/system/role.vue'),
     meta: {
       title: '角色管理',
       auth: '/system/role/query',
     },
   }
   ```

4. **配置菜单**
   ```typescript
   // 在 src/menu/modules/system.ts 中添加菜单项
   {
     path: '/system/role',
     meta: {
       title: '角色管理',
       auth: '/system/role/query',
     },
   }
   ```

### 错误处理

所有 API 调用都包含完整的错误处理：

```typescript
try {
  const res: any = await apiSystem.getMenuList(params)
  if (res.code === 0) {
    // 成功处理
  } else {
    toast.error(res.msg || '操作失败')
  }
} catch (error: any) {
  toast.error(error.message || '网络错误')
}
```

### 状态管理

使用 Pinia 进行状态管理，主要涉及：

- `userStore`: 用户信息、权限数据
- `settingsStore`: 系统设置
- `routeStore`: 路由管理
- `menuStore`: 菜单管理

## 部署说明

### 环境变量配置

```bash
# .env.development
VITE_APP_API_BASEURL=https://dev.nexthuman.cn/
VITE_OPEN_PROXY=false

# .env.production
VITE_APP_API_BASEURL=https://your-production-api.com/
```

### 构建命令

```bash
# 开发环境
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview
```

## 注意事项

1. **权限验证**: 所有敏感操作都需要验证用户权限
2. **错误处理**: API 调用必须包含完整的错误处理逻辑
3. **用户体验**: 操作成功/失败都要有明确的提示信息
4. **数据安全**: 密码等敏感信息需要 MD5 加密处理
5. **代码规范**: 遵循项目的 ESLint 和 TypeScript 规范

## 更新日志

### v1.0.0 (2024-01-XX)
- ✅ 完成菜单管理功能
- ✅ 对接后端 API 接口
- ✅ 实现权限控制系统
- ✅ 配置路由和菜单结构

---

**文档版本**: 1.0.0
**最后更新**: 2024-01-XX
**维护人员**: 开发团队
description:
globs:
alwaysApply: false
---
