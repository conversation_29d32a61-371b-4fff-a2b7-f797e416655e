# 文档

## 音频处理

### 原始音频上传

#### URL

- `POST` /audio/upload

描述：原始音频上传

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| src | string | 否 | - | 音频文件 |  |
| srcName | string | 否 | - | 原文件名（用于回显） |  |
| duration | int64 | 否 | - | 时长(ms) | 0 |

#### 响应示例

```
{
    "src": "",
    "srcName": "",
    "duration": 0
}
```

#### 错误码

无

### 上传语音并转换为wav单通道

#### URL

- `POST` /audio/upload2Wav

描述：上传语音并转换为wav单通道

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| src | string | 否 | - | 音频文件 |  |
| srcName | string | 否 | - | 原文件名（用于回显） |  |
| duration | int64 | 否 | - | 时长(ms) | 0 |

#### 响应示例

```
{
    "src": "",
    "srcName": "",
    "duration": 0
}
```

#### 错误码

无
## VideoCdn

### 临时视频上传并转换至30FPS的Mp4视频

#### URL

- `POST` /video/uploadTempTo30FPSMp4

描述：临时视频上传并转换至30FPS的Mp4视频

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cover | string | 否 | - | 封面 |  |
| src | string | 否 | - | 视频地址 |  |
| srcName | string | 否 | - | 原文件名（用于回显） |  |
| duration | int64 | 否 | - | 时长 | 0 |

#### 响应示例

```
{
    "cover": "",
    "src": "",
    "srcName": "",
    "duration": 0
}
```

#### 错误码

无

### 视频文件上传

#### URL

- `POST` /video/upload

描述：视频文件上传

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cover | string | 否 | - | 封面 |  |
| src | string | 否 | - | 视频地址 |  |
| srcName | string | 否 | - | 原文件名（用于回显） |  |
| duration | int64 | 否 | - | 时长 | 0 |

#### 响应示例

```
{
    "cover": "",
    "src": "",
    "srcName": "",
    "duration": 0
}
```

#### 错误码

无
## 通用上传(无需登录)

### 文件上传(自动识别类型)

#### URL

- `POST` /cmn/upload

描述：文件上传(自动识别类型)

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无

### 上传临时存储文件

#### URL

- `POST` /cmn/uploadTemp

描述：上传临时存储文件

ContentType：`multipart/form-data`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| file | file | 是 | - | No comments found. |  |

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
