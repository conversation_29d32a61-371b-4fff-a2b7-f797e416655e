# 文档

## 【后台】影片管理


### 电影列表
接口权限：/data/film/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/data/film/search


描述：电影列表
接口权限：/data/film/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | - | No comments found. | 0 |
| filmName | string | 否 | - | 电影名称 |  |
| version | string | 否 | - | 影片类型 |  |
| provider | string | 否 | - | 提供商 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "filmName": "",
    "version": "",
    "provider": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaCode | string | 否 | - | 影院code |  |
|   └ code | string | 否 | - | 影片编码 |  |
|   └ name | string | 否 | - | 影片名称 |  |
|   └ lang | string | 否 | - | 狱中 |  |
|   └ duration | int32 | 否 | - | 影片时长，单位分钟 | 0 |
|   └ sequence | int32 | 否 | - | 连场中的序号 | 0 |
|   └ version | string | 否 | - | 发行版本 |  |
|   └ publishDate | int64 | 否 | - | 公映日期 | 0 |
|   └ markers | array | 否 |  | 电影人员表 |  |
|     └ code | string | 否 | - | 人员编码 |  |
|     └ names | array | 否 | - | 人员名称 | , |
|     └ role | string | 否 | - | 人员角色 |  |
|   └ introduction | string | 否 | - | 简介 |  |
|   └ provider | string | 否 | - | 服务商 |  |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ cinemaCode | string | 否 | - | 影院code |  |
|   └ code | string | 否 | - | 影片编码 |  |
|   └ name | string | 否 | - | 影片名称 |  |
|   └ lang | string | 否 | - | 狱中 |  |
|   └ duration | int32 | 否 | - | 影片时长，单位分钟 | 0 |
|   └ sequence | int32 | 否 | - | 连场中的序号 | 0 |
|   └ version | string | 否 | - | 发行版本 |  |
|   └ publishDate | int64 | 否 | - | 公映日期 | 0 |
|   └ markers | array | 否 |  | 电影人员表 |  |
|     └ code | string | 否 | - | 人员编码 |  |
|     └ names | array | 否 | - | 人员名称 | , |
|     └ role | string | 否 | - | 人员角色 |  |
|   └ introduction | string | 否 | - | 简介 |  |
|   └ provider | string | 否 | - | 服务商 |  |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "cinemaCode": "",
            "code": "",
            "name": "",
            "lang": "",
            "duration": 0,
            "sequence": 0,
            "version": "",
            "publishDate": 0,
            "markers": [
                {
                    "code": "",
                    "names": [
                        0,
                        0
                    ],
                    "role": ""
                }
            ],
            "introduction": "",
            "provider": ""
        }
    ],
    "result": [
        {
            "id": "",
            "cinemaCode": "",
            "code": "",
            "name": "",
            "lang": "",
            "duration": 0,
            "sequence": 0,
            "version": "",
            "publishDate": 0,
            "markers": [
                {
                    "code": "",
                    "names": [
                        0,
                        0
                    ],
                    "role": ""
                }
            ],
            "introduction": "",
            "provider": ""
        }
    ]
}
```

#### 错误码

无
## 【后台】排期管理


### 排期列表
接口权限：/ticket/play/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/ticket/play/search


描述：排期列表
接口权限：/ticket/play/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | - | No comments found. | 0 |
| cinemaCode | string | 否 | - | 影院code |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "cinemaCode": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 场次code |  |
|   └ cinemaCode | string | 否 | - | 影院编码 |  |
|   └ screenCode | string | 否 | - | 影厅编码 |  |
|   └ startTime | int64 | 否 | - | 放映计划开始时间 | 0 |
|   └ playthroughFlag | string | 否 | - | 连场标志：Yes，连场<br>          No，非连场 |  |
|   └ marketingCode | string | 否 | - | 活动标识 |  |
|   └ marketingName | string | 否 | - | 活动名称 |  |
|   └ films | array | 否 |  | 电影 |  |
|     └ id | string | 否 | - |  |  |
|     └ code | string | 否 | - | 影片编码 |  |
|     └ name | string | 否 | - | 影片名称 |  |
|     └ lang | string | 否 | - | 狱中 |  |
|     └ duration | int32 | 否 | - | 影片时长，单位分钟 | 0 |
|     └ sequence | int32 | 否 | - | 连场中的序号 | 0 |
|     └ version | string | 否 | - | 发行版本 |  |
|     └ publishDate | string | 否 | - | 公映日期 |  |
|     └ publisher | string | 否 | - | 发行商 |  |
|     └ producer | string | 否 | - | 制片人 |  |
|     └ director | string | 否 | - | 导演 |  |
|     └ cast | string | 否 | - | 演员 |  |
|     └ introduction | string | 否 | - | 简介 |  |
|     └ provider | string | 否 | - | 供应商 |  |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ price | object | 否 |  | 影片价格 |  |
|     └ id | string | 否 | - |  |  |
|     └ lowestPrice | int64 | 否 | - | 最低票价 | 0 |
|     └ standardPrice | int64 | 否 | - | 标准票价（上报票房价格） | 0 |
|     └ listingPrice | int64 | 否 | - | 成人票价（影院成人票价，仅参考用，不作为结算依据） | 0 |
|     └ serviceAddFee | int64 | 否 | - | 增值服务费 | 0 |
|     └ cinemaAllowance | int64 | 否 | - | 影院补贴 | 0 |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ seats | array | 否 |  | 放映座位状态 |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ featureAppNo | string | 否 | - | 放映计划编码() |  |
|     └ seatCode | string | 否 | - | 座位编码 |  |
|     └ rowNum | int32 | 否 | - | 座位行号 | 0 |
|     └ columnNum | int32 | 否 | - | 座位列号 | 0 |
|     └ status | string | 否 | - | 售出状态：<br>Available，可出售<br>Locked，已锁定<br>Sold，已售出<br>Booked，已预订（留座）<br>Unavailable，不可用<br>Isolate，隔离座（座位图状态：锁定座位） |  |
|     └ memberLevelCode | string | 否 | - | 专售会员等级，无限制时，返回AllUser，仅限会员时返回AllMember，指定会员时，返回各会员等级，以英文逗号分隔 |  |
|     └ xCoord | int32 | 否 | - | 座位横坐标 | 0 |
|     └ yCoord | int32 | 否 | - | 座位纵坐标 | 0 |
|     └ level | int32 | 否 | - | 座位等级 | 0 |
|     └ levelName | string | 否 | - | 影院自定义的分区名称 |  |
|     └ provider | string | 否 | - | 供应商 |  |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ provider | string | 否 | - | No comments found. |  |
|   └ ctime | int64 | 否 | - | 更新时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 场次code |  |
|   └ cinemaCode | string | 否 | - | 影院编码 |  |
|   └ screenCode | string | 否 | - | 影厅编码 |  |
|   └ startTime | int64 | 否 | - | 放映计划开始时间 | 0 |
|   └ playthroughFlag | string | 否 | - | 连场标志：Yes，连场<br>          No，非连场 |  |
|   └ marketingCode | string | 否 | - | 活动标识 |  |
|   └ marketingName | string | 否 | - | 活动名称 |  |
|   └ films | array | 否 |  | 电影 |  |
|     └ id | string | 否 | - |  |  |
|     └ code | string | 否 | - | 影片编码 |  |
|     └ name | string | 否 | - | 影片名称 |  |
|     └ lang | string | 否 | - | 狱中 |  |
|     └ duration | int32 | 否 | - | 影片时长，单位分钟 | 0 |
|     └ sequence | int32 | 否 | - | 连场中的序号 | 0 |
|     └ version | string | 否 | - | 发行版本 |  |
|     └ publishDate | string | 否 | - | 公映日期 |  |
|     └ publisher | string | 否 | - | 发行商 |  |
|     └ producer | string | 否 | - | 制片人 |  |
|     └ director | string | 否 | - | 导演 |  |
|     └ cast | string | 否 | - | 演员 |  |
|     └ introduction | string | 否 | - | 简介 |  |
|     └ provider | string | 否 | - | 供应商 |  |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ price | object | 否 |  | 影片价格 |  |
|     └ id | string | 否 | - |  |  |
|     └ lowestPrice | int64 | 否 | - | 最低票价 | 0 |
|     └ standardPrice | int64 | 否 | - | 标准票价（上报票房价格） | 0 |
|     └ listingPrice | int64 | 否 | - | 成人票价（影院成人票价，仅参考用，不作为结算依据） | 0 |
|     └ serviceAddFee | int64 | 否 | - | 增值服务费 | 0 |
|     └ cinemaAllowance | int64 | 否 | - | 影院补贴 | 0 |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ seats | array | 否 |  | 放映座位状态 |  |
|     └ id | string | 否 | - | No comments found. |  |
|     └ featureAppNo | string | 否 | - | 放映计划编码() |  |
|     └ seatCode | string | 否 | - | 座位编码 |  |
|     └ rowNum | int32 | 否 | - | 座位行号 | 0 |
|     └ columnNum | int32 | 否 | - | 座位列号 | 0 |
|     └ status | string | 否 | - | 售出状态：<br>Available，可出售<br>Locked，已锁定<br>Sold，已售出<br>Booked，已预订（留座）<br>Unavailable，不可用<br>Isolate，隔离座（座位图状态：锁定座位） |  |
|     └ memberLevelCode | string | 否 | - | 专售会员等级，无限制时，返回AllUser，仅限会员时返回AllMember，指定会员时，返回各会员等级，以英文逗号分隔 |  |
|     └ xCoord | int32 | 否 | - | 座位横坐标 | 0 |
|     └ yCoord | int32 | 否 | - | 座位纵坐标 | 0 |
|     └ level | int32 | 否 | - | 座位等级 | 0 |
|     └ levelName | string | 否 | - | 影院自定义的分区名称 |  |
|     └ provider | string | 否 | - | 供应商 |  |
|     └ mtime | int64 | 否 | - | 更新时间 | 0 |
|   └ provider | string | 否 | - | No comments found. |  |
|   └ ctime | int64 | 否 | - | 更新时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "code": "",
            "cinemaCode": "",
            "screenCode": "",
            "startTime": 0,
            "playthroughFlag": "",
            "marketingCode": "",
            "marketingName": "",
            "films": [
                {
                    "id": "",
                    "code": "",
                    "name": "",
                    "lang": "",
                    "duration": 0,
                    "sequence": 0,
                    "version": "",
                    "publishDate": "",
                    "publisher": "",
                    "producer": "",
                    "director": "",
                    "cast": "",
                    "introduction": "",
                    "provider": "",
                    "mtime": 0
                }
            ],
            "price": {
                "id": "",
                "lowestPrice": 0,
                "standardPrice": 0,
                "listingPrice": 0,
                "serviceAddFee": 0,
                "cinemaAllowance": 0,
                "mtime": 0
            },
            "seats": [
                {
                    "id": "",
                    "featureAppNo": "",
                    "seatCode": "",
                    "rowNum": 0,
                    "columnNum": 0,
                    "status": "",
                    "memberLevelCode": "",
                    "xCoord": 0,
                    "yCoord": 0,
                    "level": 0,
                    "levelName": "",
                    "provider": "",
                    "mtime": 0
                }
            ],
            "provider": "",
            "ctime": 0,
            "uptime": 0
        }
    ],
    "result": [
        {
            "id": "",
            "code": "",
            "cinemaCode": "",
            "screenCode": "",
            "startTime": 0,
            "playthroughFlag": "",
            "marketingCode": "",
            "marketingName": "",
            "films": [
                {
                    "id": "",
                    "code": "",
                    "name": "",
                    "lang": "",
                    "duration": 0,
                    "sequence": 0,
                    "version": "",
                    "publishDate": "",
                    "publisher": "",
                    "producer": "",
                    "director": "",
                    "cast": "",
                    "introduction": "",
                    "provider": "",
                    "mtime": 0
                }
            ],
            "price": {
                "id": "",
                "lowestPrice": 0,
                "standardPrice": 0,
                "listingPrice": 0,
                "serviceAddFee": 0,
                "cinemaAllowance": 0,
                "mtime": 0
            },
            "seats": [
                {
                    "id": "",
                    "featureAppNo": "",
                    "seatCode": "",
                    "rowNum": 0,
                    "columnNum": 0,
                    "status": "",
                    "memberLevelCode": "",
                    "xCoord": 0,
                    "yCoord": 0,
                    "level": 0,
                    "levelName": "",
                    "provider": "",
                    "mtime": 0
                }
            ],
            "provider": "",
            "ctime": 0,
            "uptime": 0
        }
    ]
}
```

#### 错误码

无
