# 文档

## 【后台】影院管理

### 影院列表
接口权限：/system/menu/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/admin_cinema/search

描述：影院列表
接口权限：/system/menu/query

ContentType：`application/json`

#### 请求参数

##### Body Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| page | int32 | 否 | - | No comments found. | 0 |
| size | int32 | 否 | 1000 | No comments found. | 0 |
| cinemaName | string | 否 | - | 影院名称 |  |
| cinemaCode | string | 否 | - | 影院编码 |  |
| city | string | 否 | - | 所在城市 |  |
| provider | string | 否 | - | 提供商 |  |

#### 请求示例

```
{
    "page": 0,
    "size": 0,
    "cinemaName": "",
    "cinemaCode": "",
    "city": "",
    "provider": ""
}
```

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 影院编码 |  |
|   └ name | string | 否 | - | 影院名称 |  |
|   └ province | string | 否 | - | 影院所在省 |  |
|   └ city | string | 否 | - | 影院所在城市 |  |
|   └ createDate | string | 否 | - | 电影院授权日期 |  |
|   └ priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
|   └ address | string | 否 | - | 影院地址 |  |
|   └ screenCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |
|   └ syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
|   └ qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
|   └ provider | string | 否 | - | No comments found. |  |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 影院编码 |  |
|   └ name | string | 否 | - | 影院名称 |  |
|   └ province | string | 否 | - | 影院所在省 |  |
|   └ city | string | 否 | - | 影院所在城市 |  |
|   └ createDate | string | 否 | - | 电影院授权日期 |  |
|   └ priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
|   └ address | string | 否 | - | 影院地址 |  |
|   └ screenCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |
|   └ syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
|   └ qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
|   └ provider | string | 否 | - | No comments found. |  |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "code": "",
            "name": "",
            "province": "",
            "city": "",
            "createDate": "",
            "priceType": 0,
            "address": "",
            "screenCount": 0,
            "version": 0,
            "ctime": 0,
            "uptime": 0,
            "syncStatus": 0,
            "qrCodeUrl": "",
            "provider": ""
        }
    ],
    "result": [
        {
            "id": "",
            "code": "",
            "name": "",
            "province": "",
            "city": "",
            "createDate": "",
            "priceType": 0,
            "address": "",
            "screenCount": 0,
            "version": 0,
            "ctime": 0,
            "uptime": 0,
            "syncStatus": 0,
            "qrCodeUrl": "",
            "provider": ""
        }
    ]
}
```

#### 错误码

无

### 影院列表(所有)
接口权限：/system/menu/query

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/admin_cinema/listAll

描述：影院列表(所有)
接口权限：/system/menu/query

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| total | int64 | 否 | - | No comments found. | 0 |
| content | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 影院编码 |  |
|   └ name | string | 否 | - | 影院名称 |  |
|   └ province | string | 否 | - | 影院所在省 |  |
|   └ city | string | 否 | - | 影院所在城市 |  |
|   └ createDate | string | 否 | - | 电影院授权日期 |  |
|   └ priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
|   └ address | string | 否 | - | 影院地址 |  |
|   └ screenCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |
|   └ syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
|   └ qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
|   └ provider | string | 否 | - | No comments found. |  |
| result | array | 否 |  | No comments found. |  |
|   └ id | string | 否 | - | No comments found. |  |
|   └ code | string | 否 | - | 影院编码 |  |
|   └ name | string | 否 | - | 影院名称 |  |
|   └ province | string | 否 | - | 影院所在省 |  |
|   └ city | string | 否 | - | 影院所在城市 |  |
|   └ createDate | string | 否 | - | 电影院授权日期 |  |
|   └ priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
|   └ address | string | 否 | - | 影院地址 |  |
|   └ screenCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
|   └ ctime | int64 | 否 | - | 创建时间 | 0 |
|   └ uptime | int64 | 否 | - | 更新时间 | 0 |
|   └ syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
|   └ qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
|   └ provider | string | 否 | - | No comments found. |  |

#### 响应示例

```
{
    "total": 0,
    "content": [
        {
            "id": "",
            "code": "",
            "name": "",
            "province": "",
            "city": "",
            "createDate": "",
            "priceType": 0,
            "address": "",
            "screenCount": 0,
            "version": 0,
            "ctime": 0,
            "uptime": 0,
            "syncStatus": 0,
            "qrCodeUrl": "",
            "provider": ""
        }
    ],
    "result": [
        {
            "id": "",
            "code": "",
            "name": "",
            "province": "",
            "city": "",
            "createDate": "",
            "priceType": 0,
            "address": "",
            "screenCount": 0,
            "version": 0,
            "ctime": 0,
            "uptime": 0,
            "syncStatus": 0,
            "qrCodeUrl": "",
            "provider": ""
        }
    ]
}
```

#### 错误码

无

### 编辑菜单
接口权限：/system/menu/save

#### URL

- 测试环境: `GET` https://dev.nexthuman.cn/zfilm/api/admin_cinema/info

描述：编辑菜单
接口权限：/system/menu/save

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

##### Query Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cinemaCode | string | 否 | - | No comments found. |  |

#### 响应参数

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| id | string | 否 | - | No comments found. |  |
| code | string | 否 | - | 影院编码 |  |
| name | string | 否 | - | 影院名称 |  |
| province | string | 否 | - | 影院所在省 |  |
| city | string | 否 | - | 影院所在城市 |  |
| createDate | string | 否 | - | 电影院授权日期 |  |
| priceType | int32 | 否 | - | 定价模式，0-影院定价，1-合作商定价 | 0 |
| address | string | 否 | - | 影院地址 |  |
| screenCount | int32 | 否 | - | 影厅数量 | 0 |
| screens | array | 否 |  | 影厅 |  |
|   └ screenCode | string | 否 | - | 影厅编码 |  |
|   └ screenName | string | 否 | - | 影厅名称 |  |
|   └ seatCount | int32 | 否 | - | 影厅数量 | 0 |
|   └ type | string | 否 | - | 影厅类型：Normal，普通影厅<br>          3D，3D 影厅<br>          MAX，巨幕影厅<br>          MAX3D， 3D 巨幕影厅 |  |
| version | int32 | 否 | - | 系统版本<br>1、UsbKey13规范影院<br>2、CA证书23规范影院（上报票房与座位图需要后面增加行列号、座位编码保持20位） | 0 |
| ctime | int64 | 否 | - | 创建时间 | 0 |
| uptime | int64 | 否 | - | 更新时间 | 0 |
| syncStatus | int64 | 否 | - | 状态 1正常 0被隔离 | 0 |
| qrCodeUrl | string | 否 | - | 影院拉新二维码 |  |
| provider | string | 否 | - | No comments found. |  |

#### 响应示例

```
{
    "id": "",
    "code": "",
    "name": "",
    "province": "",
    "city": "",
    "createDate": "",
    "priceType": 0,
    "address": "",
    "screenCount": 0,
    "screens": [
        {
            "screenCode": "",
            "screenName": "",
            "seatCount": 0,
            "type": ""
        }
    ],
    "version": 0,
    "ctime": 0,
    "uptime": 0,
    "syncStatus": 0,
    "qrCodeUrl": "",
    "provider": ""
}
```

#### 错误码

无

### 生成二维码

#### URL

- 测试环境: `POST` https://dev.nexthuman.cn/zfilm/api/admin_cinema/create/qrcode

描述：生成二维码

ContentType：`application/x-www-form-urlencoded;charset=UTF-8`

#### 请求参数

##### Query Parameter

| 名称 | 类型 | 必填 | 最大长度 | 描述 | 示例值 |
| --- | --- | --- | --- | --- | --- |
| cinemaCode | string | 否 | - | 影院编码 |  |

#### 响应参数

无

#### 响应示例

```
{}
```

#### 错误码

无
