import type { RouteRecordRaw } from 'vue-router'

const cinema: RouteRecordRaw = {
  path: '/cinema',
  component: () => import('@/layouts/index.vue'),
  redirect: '/cinema/index',
  name: 'Cinema',
  meta: {
    title: '影院管理',
    icon: 'ep:video-camera',
    // auth: '/adm_cinema',
  },
  children: [
    {
      path: 'index',
      name: 'CinemaIndex',
      component: () => import('@/views/cinema/cinema.vue'),
      meta: {
        title: '影院列表',
        icon: 'ep:film',
        auth: '/adm_cinema/query',
        // activeMenu: '/cinema',
      },
    },
    {
      path: 'film',
      name: 'CinemaFilm',
      component: () => import('@/views/cinema/film.vue'),
      meta: {
        title: '影片管理',
        icon: 'i-ri:film-line',
        auth: '/data/film/query',
      },
    },
    {
      path: 'schedule',
      name: 'CinemaSchedule',
      component: () => import('@/views/cinema/schedule.vue'),
      meta: {
        title: '场次管理',
        icon: 'i-ri:calendar-schedule-line',
        auth: '/ticket/play/query',
      },
    },
  ],
}

export default cinema
