import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const routes: RouteRecordRaw = {
  path: '/system',
  component: Layout,
  name: 'system',
  meta: {
    title: '用户管理',
    icon: 'i-ri:users-3-line',
  },
  children: [
    {
      path: 'users',
      name: 'systemUsers',
      component: () => import('@/views/user/users.vue'),
      meta: {
        title: '用户管理',
        icon: 'i-ri:user-line',
        auth: '/system/account/query',
      },
    },
  ],
}

export default routes
