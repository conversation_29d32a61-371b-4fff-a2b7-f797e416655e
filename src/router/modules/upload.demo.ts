import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const UploadDemo: RouteRecordRaw = {
  path: '/demonstration/upload',
  name: 'uploadDemo',
  component: Layout,
  meta: {
    title: '上传组件演示',
    icon: 'i-ri:upload-cloud-2-line',
  },
  children: [
    {
      path: '',
      name: 'uploadDemoD',
      component: () => import('@/views/demonstration/UploadDemo.vue'),
      meta: {
        title: '上传组件演示',
      },
    },
  ],
}

export default UploadDemo
