import type { RouteRecordRaw } from 'vue-router'

function Layout() {
  return import('@/layouts/index.vue')
}

const UploadDemo: RouteRecordRaw = {
  path: '/DictSelectorDemo',
  name: 'DictSelectorDemo',
  component: Layout,
  meta: {
    title: '字典选择器组件演示',
    icon: 'i-ri:upload-cloud-2-line',
  },
  children: [
    {
      path: '',
      name: 'DictSelectorDemoD',
      component: () => import('@/views/system/DictSelector.demo.vue'),
      meta: {
        title: '字典选择器组件演示',
      },
    },
  ],
}

export default UploadDemo
