<script setup lang="ts">
import type { UploadAjaxError, UploadFile, UploadProps, UploadRawFile } from 'element-plus'
// 删除此行导入
// import { ref, onMounted } from 'vue';
import type { PropType } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { ElButton, ElDialog, ElMessage, ElUpload } from 'element-plus'
import { commonUpload } from '@/api/modules/media'

const props = defineProps({
  uploadText: {
    type: String,
    default: '通用上传',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  headers: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({}),
  },
  accept: {
    type: String,
    default: '*/*',
  },
})

const emits = defineEmits(['onSuccess', 'onRemove'])
const fileList = ref<UploadFile[]>([])

onMounted(() => {
  // CommonUpload组件已挂载
})
const previewDialogVisible = ref(false)
const previewUrl = ref('')
const previewType = ref('')

function beforeUpload(rawFile: UploadRawFile) {
  // console.log('beforeUpload方法被触发')
  if (props.accept !== '*/*') {
    // 处理文件扩展名检查
    const acceptExtensions = props.accept.split(',').map(ext => ext.replace(/^\./, '').toLowerCase())
    const fileExtension = rawFile.name.split('.').pop()?.toLowerCase()
    const isAcceptable = acceptExtensions.includes(fileExtension)
    if (!isAcceptable) {
      ElMessage.error(`请上传${props.accept}类型的文件`)
      return false
    }
  }
  return true
}

const handleUpload: UploadProps['httpRequest'] = async (options) => {
  const { file } = options
  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await commonUpload(formData)
    options.onSuccess(response.data)
    ElMessage.success('上传成功')
  }
  catch (error: any) {
    options.onError(error)
    // 针对接口不存在的情况做特殊提示
    if (error?.response?.data?.errorCode === 'NOT_FOUND') {
      ElMessage.error('上传接口不存在，请联系管理员')
    }
    else {
      ElMessage.error('上传失败，请重试')
    }
  }
}

function handleSuccess(response: any, file: UploadFile) {
  emits('onSuccess', response, file)
}

function handleError(err: UploadAjaxError, _file: UploadFile) {
  console.error('上传失败:', err)
}

function handleRemove(_file: UploadFile) {
  emits('onRemove', _file)
  return true
}

function handlePreview(file: UploadFile) {
  if (file.raw) {
    previewUrl.value = URL.createObjectURL(file.raw)
    previewType.value = file.raw.type.split('/')[0] || ''
    previewDialogVisible.value = true
  }
}
</script>

<template>
  <ElUpload
    v-model:file-list="fileList"
    :http-request="handleUpload"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-remove="handleRemove"
    :show-file-list="true"
    :headers="headers"
    :accept="accept"
    :disabled="disabled"
  >
    <ElButton :disabled="disabled" type="primary">
      <Upload class="mr-2" />
      {{ uploadText }}
    </ElButton>
    <template #file="{ file }">
      <div class="file-item">
        <div class="file-name">
          {{ file.name }}
        </div>
        <div class="file-actions">
          <ElButton
            v-if="file.status === 'success'"
            size="small"
            type="text"
            @click.stop="handlePreview(file)"
          >
            预览
          </ElButton>
          <ElButton size="small" link @click.stop="handleRemove(file)">
            移除
          </ElButton>
        </div>
      </div>
    </template>
  </ElUpload>

  <ElDialog v-model="previewDialogVisible" title="文件预览">
    <div class="preview-container">
      <template v-if="previewType === 'image'">
        <img :src="previewUrl" class="media-preview" alt="预览图片">
      </template>
      <template v-else-if="previewType === 'audio'">
        <audio :src="previewUrl" controls class="media-preview" />
      </template>
      <template v-else-if="previewType === 'video'">
        <video :src="previewUrl" controls class="media-preview" />
      </template>
      <template v-else>
        <div>不支持的文件预览类型</div>
      </template>
    </div>
  </ElDialog>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.preview-container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.media-preview {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
}
</style>
