<script setup lang="ts">
import type { UploadFile, UploadProps, UploadRawFile } from 'element-plus'
import type { PropType } from 'vue'
import { Upload } from '@element-plus/icons-vue'
import { ElButton, ElDialog, ElMessage, ElUpload } from 'element-plus'
import { ref } from 'vue'
import { uploadVideoTempTo30FPSMp4 } from '@/api/modules/media'

const _props = defineProps({
  uploadText: {
    type: String,
    default: '上传视频',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  headers: {
    type: Object as PropType<Record<string, string>>,
    default: () => ({}),
  },
})

const emits = defineEmits(['onSuccess', 'onRemove'])
const fileList = ref<UploadFile[]>([])
const previewDialogVisible = ref(false)
const previewVideoUrl = ref('')

function beforeUpload(rawFile: UploadRawFile) {
  const isVideo = rawFile.type?.startsWith('video/')
  if (!isVideo) {
    ElMessage.error('请上传视频文件')
    return false
  }
  return true
}

const handleUpload: UploadProps['httpRequest'] = async (options) => {
  const { file } = options
  const formData = new FormData()
  formData.append('file', file as File)

  try {
    const response = await uploadVideoTempTo30FPSMp4(formData)
    options.onSuccess(response.data)
    ElMessage.success('上传成功')
  }
  catch (error) {
    options.onError(error as any)
    ElMessage.error('上传失败，请重试')
  }
}

function handleSuccess(response: any, file: UploadFile) {
  emits('onSuccess', response, file)
}

function handleError(error: any) {
  console.error('上传失败:', error)
}

function handleRemove(file: UploadFile) {
  emits('onRemove', file)
  return true
}

function handlePreview(file: UploadFile) {
  if (file.raw) {
    previewVideoUrl.value = URL.createObjectURL(file.raw)
    previewDialogVisible.value = true
  }
}
</script>

<template>
  <ElUpload
    v-model:file-list="fileList"
    :http-request="handleUpload"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-error="handleError"
    :on-remove="handleRemove"
    :show-file-list="true"
    :headers="headers"
    accept="video/*"
    :disabled="disabled"
  >
    <ElButton :disabled="disabled" type="primary">
      <Upload class="mr-2" />
      {{ uploadText }}
    </ElButton>
    <template #file="{ file }">
      <div class="file-item">
        <div class="file-name">
          {{ file.name }}
        </div>
        <div class="file-actions">
          <ElButton
            v-if="file.status === 'success'"
            size="small"
            type="text"
            @click.stop="handlePreview(file)"
          >
            预览
          </ElButton>
          <ElButton size="small" link @click.stop="handleRemove(file)">
            移除
          </ElButton>
        </div>
      </div>
    </template>
  </ElUpload>

  <ElDialog v-model="previewDialogVisible" title="视频预览" width="80%">
    <video :src="previewVideoUrl" controls class="video-preview" />
  </ElDialog>
</template>

<style scoped lang="scss">
.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.video-preview {
  width: 100%;
  max-height: 500px;
  margin: 0 auto;
  object-fit: contain;
}
</style>
