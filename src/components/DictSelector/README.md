# DictSelector 字典选择器组件

基于 Element Plus 的 el-select 封装的字典选择器组件，用于快速选择系统字典项。

## 功能特性

- 🚀 自动加载字典项数据
- 🔄 支持单选和多选模式
- 🎯 支持自定义值和标签字段
- 🔍 支持搜索过滤
- 🎨 支持所有 el-select 的样式配置
- 📊 自动按排序字段排序
- 🔒 支持显示/隐藏禁用项

## Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| dictType | 字典类型（必填） | string | - | - |
| modelValue | 绑定值 | string \| number \| string[] \| number[] | - | undefined |
| multiple | 是否多选 | boolean | - | false |
| placeholder | 占位符 | string | - | '请选择' |
| clearable | 是否可清空 | boolean | - | true |
| disabled | 是否禁用 | boolean | - | false |
| size | 尺寸 | string | large/default/small | default |
| filterable | 是否可搜索 | boolean | - | false |
| valueKey | 值字段 | string | dictValue/dictLabel | dictValue |
| labelKey | 显示字段 | string | dictLabel/dictValue | dictLabel |
| showAll | 是否显示禁用项 | boolean | - | false |

## Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 值变化时触发 | (value) |
| change | 选择项变化时触发 | (value, option) |

## Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| refresh | 刷新字典项数据 | - |
| getDictItems | 获取字典项数据 | - |

## 使用示例

### 基础用法

```vue
<template>
  <DictSelector
    v-model="selectedValue"
    dict-type="user_status"
    placeholder="请选择用户状态"
  />
</template>

<script setup>
import DictSelector from '@/components/DictSelector/index.vue'

const selectedValue = ref('')
</script>
```

### 多选模式

```vue
<template>
  <DictSelector
    v-model="selectedValues"
    dict-type="user_roles"
    multiple
    placeholder="请选择用户角色"
  />
</template>

<script setup>
const selectedValues = ref([])
</script>
```

### 自定义字段

```vue
<template>
  <!-- 使用 dictLabel 作为值，dictValue 作为显示文本 -->
  <DictSelector
    v-model="selectedValue"
    dict-type="priority_level"
    value-key="dictLabel"
    label-key="dictValue"
  />
</template>
```

### 可搜索

```vue
<template>
  <DictSelector
    v-model="selectedValue"
    dict-type="city_list"
    filterable
    placeholder="请输入城市名称"
  />
</template>
```

### 显示禁用项

```vue
<template>
  <DictSelector
    v-model="selectedValue"
    dict-type="all_status"
    show-all
    placeholder="请选择状态（包含禁用）"
  />
</template>
```

### 监听变化事件

```vue
<template>
  <DictSelector
    v-model="selectedValue"
    dict-type="user_type"
    @change="handleChange"
  />
</template>

<script setup>
function handleChange(value, option) {
  console.log('选中值:', value)
  console.log('选中项:', option)
  if (option) {
    console.log('字典标签:', option.dictLabel)
    console.log('字典值:', option.dictValue)
    console.log('描述:', option.description)
  }
}
</script>
```

## 注意事项

1. `dictType` 是必填参数，必须对应系统中已存在的字典类型
2. 组件会自动过滤掉状态为禁用的字典项，除非设置 `showAll=true`
3. 字典项会按照 `sort` 字段自动排序
4. 组件支持响应式更新，当字典类型变化时会自动重新加载数据
5. 可以通过 `ref` 调用 `refresh()` 方法手动刷新数据

## API 接口依赖

组件依赖以下 API 接口：

- `apiSystem.getDictItemsByType()` - 根据字典类型获取字典项

确保后端已实现对应的接口。
