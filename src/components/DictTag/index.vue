<script setup lang="ts">
// import { onMounted, ref, watch } from 'vue'
// TODO: 暂时注释掉，等API接口完成后再启用
// import apiSystem from '@/api/modules/system'

defineOptions({
  name: 'DictTag',
})

// Props
const props = withDefaults(
  defineProps<{
    dictType: string // 字典类型
    modelValue?: string | number | string[] | number[] // 选中的值
    multiple?: boolean // 是否多选
    placeholder?: string // 占位符
    clearable?: boolean // 是否可清空
    disabled?: boolean // 是否禁用
    size?: 'large' | 'default' | 'small' // 尺寸
    filterable?: boolean // 是否可搜索
    valueKey?: 'dictValue' | 'dictLabel' // 使用哪个字段作为值，默认使用dictValue
    labelKey?: 'dictLabel' | 'dictValue' // 使用哪个字段作为显示文本，默认使用dictLabel
    showAll?: boolean // 是否显示所有状态的字典项（包括禁用的）
  }>(),
  {
    modelValue: undefined,
    multiple: false,
    placeholder: '请选择',
    clearable: true,
    disabled: false,
    size: 'default',
    filterable: false,
    valueKey: 'dictValue',
    labelKey: 'dictLabel',
    showAll: false,
  },
)

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string | number | string[] | number[] | undefined]
  'change': [value: string | number | string[] | number[] | undefined, option: DictItem | DictItem[] | undefined]
}>()

// 字典项接口
interface DictItem {
  id: string
  dictType: string
  dictLabel: string
  dictValue: string
  description?: string
  status: number
  sort?: number
}

// 响应式数据
const loading = ref(false)
const dictItems = ref<DictItem[]>([])

// 计算属性 - 过滤后的字典项
const filteredDictItems = computed(() => {
  if (props.showAll) {
    return dictItems.value
  }
  return dictItems.value.filter(item => item.status === 1)
})

// 计算属性 - 选项列表
const options = computed(() => {
  return [...filteredDictItems.value]
    .sort((a, b) => (a.sort || 0) - (b.sort || 0))
    .map(item => ({
      label: item[props.labelKey],
      value: item[props.valueKey],
      disabled: item.status === 0,
      raw: item,
    }))
})

// 获取字典项列表
async function getDictItems() {
  if (!props.dictType) {
    return
  }

  loading.value = true
  try {
    // TODO: 暂时注释掉API调用，使用模拟数据
    // const res: any = await apiSystem.getDictItemsByType({
    //   dictType: props.dictType,
    //   status: props.showAll ? undefined : 1,
    // })
    // if (res.code === 0) {
    //   dictItems.value = res.data || []
    // }
    // else {
    //   console.error('获取字典项失败:', res.msg)
    //   dictItems.value = []
    // }

    // 模拟字典项数据
    const mockItemsMap: Record<string, DictItem[]> = {
      user_status: [
        {
          id: '1',
          dictType: 'user_status',
          dictLabel: '正常',
          dictValue: '1',
          description: '用户状态正常',
          status: 1,
          sort: 1,
        },
        {
          id: '2',
          dictType: 'user_status',
          dictLabel: '禁用',
          dictValue: '0',
          description: '用户被禁用',
          status: 1,
          sort: 2,
        },
        {
          id: '3',
          dictType: 'user_status',
          dictLabel: '锁定',
          dictValue: '2',
          description: '用户被锁定',
          status: 1,
          sort: 3,
        },
      ],
      user_type: [
        {
          id: '4',
          dictType: 'user_type',
          dictLabel: '管理员',
          dictValue: 'admin',
          description: '系统管理员',
          status: 1,
          sort: 1,
        },
        {
          id: '5',
          dictType: 'user_type',
          dictLabel: '普通用户',
          dictValue: 'user',
          description: '普通用户',
          status: 1,
          sort: 2,
        },
        {
          id: '6',
          dictType: 'user_type',
          dictLabel: '访客',
          dictValue: 'guest',
          description: '访客用户',
          status: 0,
          sort: 3,
        },
      ],
      priority_level: [
        {
          id: '7',
          dictType: 'priority_level',
          dictLabel: '高',
          dictValue: 'high',
          description: '高优先级',
          status: 1,
          sort: 1,
        },
        {
          id: '8',
          dictType: 'priority_level',
          dictLabel: '中',
          dictValue: 'medium',
          description: '中等优先级',
          status: 1,
          sort: 2,
        },
        {
          id: '9',
          dictType: 'priority_level',
          dictLabel: '低',
          dictValue: 'low',
          description: '低优先级',
          status: 1,
          sort: 3,
        },
      ],
      order_status: [
        {
          id: '10',
          dictType: 'order_status',
          dictLabel: '待支付',
          dictValue: 'pending',
          description: '订单待支付',
          status: 1,
          sort: 1,
        },
        {
          id: '11',
          dictType: 'order_status',
          dictLabel: '已支付',
          dictValue: 'paid',
          description: '订单已支付',
          status: 1,
          sort: 2,
        },
        {
          id: '12',
          dictType: 'order_status',
          dictLabel: '已取消',
          dictValue: 'cancelled',
          description: '订单已取消',
          status: 1,
          sort: 3,
        },
      ],
      payment_method: [
        {
          id: '13',
          dictType: 'payment_method',
          dictLabel: '微信支付',
          dictValue: 'wechat',
          description: '微信支付',
          status: 1,
          sort: 1,
        },
        {
          id: '14',
          dictType: 'payment_method',
          dictLabel: '支付宝',
          dictValue: 'alipay',
          description: '支付宝支付',
          status: 1,
          sort: 2,
        },
        {
          id: '15',
          dictType: 'payment_method',
          dictLabel: '银行卡',
          dictValue: 'bank',
          description: '银行卡支付',
          status: 1,
          sort: 3,
        },
      ],
    }

    dictItems.value = mockItemsMap[props.dictType] || []
  }
  catch (error: any) {
    console.error('获取字典项失败:', error.message)
    dictItems.value = []
  }
  finally {
    loading.value = false
  }
}

// 处理值变化
function handleChange(value: string | number | string[] | number[] | undefined) {
  emit('update:modelValue', value)

  // 查找对应的选项
  let option: DictItem | DictItem[] | undefined
  if (props.multiple && Array.isArray(value)) {
    option = dictItems.value.filter(item => (value as (string | number)[]).includes(item[props.valueKey]))
  }
  else {
    option = dictItems.value.find(item => item[props.valueKey] === value)
  }

  emit('change', value, option)
}

// 监听字典类型变化
watch(
  () => props.dictType,
  () => {
    getDictItems()
  },
  { immediate: true },
)

// 监听showAll变化
watch(
  () => props.showAll,
  () => {
    getDictItems()
  },
)

// 暴露方法
defineExpose({
  refresh: getDictItems,
  getDictItems,
})
</script>

<template>
  <el-select
    :model-value="modelValue"
    :multiple="multiple"
    :placeholder="placeholder"
    :clearable="clearable"
    :disabled="disabled"
    :size="size"
    :filterable="filterable"
    :loading="loading"
    @update:model-value="handleChange"
  >
    <el-option
      v-for="option in options"
      :key="option.value"
      :label="option.label"
      :value="option.value"
      :disabled="option.disabled"
    >
      <span>{{ option.label }}</span>
      <span v-if="option.raw.description" class="ml-2 text-xs text-gray-400">
        {{ option.raw.description }}
      </span>
    </el-option>
  </el-select>
</template>
