import apiUser from '@/api/modules/user'
import router from '@/router'

export const useUserStore = defineStore(
  // 唯一ID
  'user',
  () => {
    const settingsStore = useSettingsStore()
    const routeStore = useRouteStore()
    const menuStore = useMenuStore()
    const tabbarStore = useTabbarStore()

    const account = ref(localStorage.account ?? '')
    const roles = ref<any[]>([])
    const token = ref(localStorage.token ?? '')
    const avatar = ref(localStorage.avatar ?? '')
    const permissions = ref<string[]>([])
    const isLogin = computed(() => {
      if (token.value) {
        return true
      }
      return false
    })

    // 登录
    async function login(data: {
      account: string
      password: string
    }) {
      try {
        const res: any = await apiUser.login(data)
        // 新接口：code=0为成功，data为token字符串
        if (res.code === 0 && typeof res.data === 'string') {
          // localStorage.setItem('account', data.account)
          localStorage.setItem('token', res.data)
          // account.value = data.account
          token.value = res.data
          // avatar 由 getMyInfo 再获取
        }
        else {
          // 登录失败，弹出后端msg
          if (res.msg) {
            // @ts-expect-error: window['$toast'] 不是全局类型声明，动态判断
            window.$toast ? window.$toast.error(res.msg) : (await import('vue-sonner')).toast.error(res.msg)
          }
          else {
            // @ts-expect-error: window['$toast'] 不是全局类型声明，动态判断
            window.$toast ? window.$toast.error('登录失败') : (await import('vue-sonner')).toast.error('登录失败')
          }
          throw new Error(res.msg || '登录失败')
        }
      }
      catch (err: any) {
        // 网络或其他异常
        if (err && err.message) {
          // @ts-expect-error: window['$toast'] 不是全局类型声明，动态判断
          window.$toast ? window.$toast.error(err.message) : (await import('vue-sonner')).toast.error(err.message)
        }
        throw err
      }
    }

    // 手动登出
    function logout(redirect = router.currentRoute.value.fullPath) {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      localStorage.removeItem('token')
      token.value = ''
      router.push({
        name: 'login',
        query: {
          ...(redirect !== settingsStore.settings.home.fullPath && router.currentRoute.value.name !== 'login' && { redirect }),
        },
      }).then(logoutCleanStatus)
    }
    // 请求登出
    function requestLogout() {
      // 此处仅清除计算属性 isLogin 中判断登录状态过期的变量，以保证在弹出登录窗口模式下页面展示依旧正常
      localStorage.removeItem('token')
      token.value = ''
      router.push({
        name: 'login',
        query: {
          ...(
            router.currentRoute.value.fullPath !== settingsStore.settings.home.fullPath
            && router.currentRoute.value.name !== 'login'
            && {
              redirect: router.currentRoute.value.fullPath,
            }
          ),
        },
      }).then(logoutCleanStatus)
    }
    // 登出后清除状态
    function logoutCleanStatus() {
      localStorage.removeItem('account')
      localStorage.removeItem('avatar')
      account.value = ''
      avatar.value = ''
      permissions.value = []
      settingsStore.updateSettings({}, true)
      tabbarStore.clean()
      routeStore.removeRoutes()
      menuStore.setActived(0)
    }

    // 获取权限
    async function getPermissions() {
      const res = await apiUser.permission()
      const { data } = res
      const { perms, roles, id, account: accountName, avatar: avatarName } = data
      console.log('获取权限:', data)
      // console.log(res)
      permissions.value = perms
      account.value = {
        id,
        account: accountName,
        avatar: avatarName,
      }

      setRoles(roles)
    }
    // {
    //   "id": "",
    //     "password": ""
    // }

    // 修改密码
    async function editPassword(data: {
      password: string
      newPassword: string
    }) {
      console.log(account.value)
      await apiUser.passwordEdit(data)
    }

    async function setRoles(newRoles: string[]) {
      roles.value = newRoles
    }

    return {
      account,
      token,
      avatar,
      permissions,
      isLogin,
      roles,
      login,
      logout,
      requestLogout,
      getPermissions,
      editPassword,
    }
  },
)
