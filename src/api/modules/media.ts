import request from '@/api/index'

/**
 * 音频上传接口
 * @param {FormData} data - 包含音频文件的FormData对象
 * @returns {Promise} 返回接口响应数据
 */
export function uploadAudio(data: FormData) {
  return request({
    url: '/audio/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 视频临时上传并转换为30FPS MP4接口
 * @param {FormData} data - 包含视频文件的FormData对象
 * @returns {Promise} 返回接口响应数据
 */
export function uploadVideoTempTo30FPSMp4(data: FormData) {
  return request({
    url: '/video/uploadTempTo30FPSMp4',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}

/**
 * 通用上传接口
 * @param {FormData} data - 包含上传文件的FormData对象
 * @returns {Promise} 返回接口响应数据
 */
export function commonUpload(data: FormData) {
  return request({
    url: '/cmn/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  })
}
