import md5 from 'md5'
import api from '../index'

export default {
  // 登录（对接新接口）
  login: (data: {
    account: string
    password: string
  }) => api.post(
    '/adm_account/signInWithPassword',
    {
      account: data.account,
      password: md5(data.password), // 需md5加密
    },
    {
      headers: { 'Content-Type': 'application/json' },
    },
  ),

  // 获取权限（对接新接口）
  permission: () => api.post(
    '/adm_account/getMyInfo',
    {},
    {
    },
  ),

  // 修改密码
  passwordEdit: (data: {
    password: string
    newPassword: string
  }) => api.post('/adm_account/resetPassword', data, {
    // baseURL: '/mock/',
  }),
}
