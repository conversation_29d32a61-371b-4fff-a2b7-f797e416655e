<script setup lang="ts">
import { ElCard } from 'element-plus'
import { ref } from 'vue'
import AudioUpload from '@/components/AudioUpload/index.vue'
import CommonUpload from '@/components/CommonUpload/index.vue'
import VideoUpload from '@/components/VideoUpload/index.vue'

const audioResult = ref(null)
const videoResult = ref(null)
const commonResult = ref(null)

function handleAudioSuccess(response: any) {
  audioResult.value = response
}

function handleAudioRemove() {
  audioResult.value = null
}

function handleVideoSuccess(response: any) {
  videoResult.value = response
}

function handleVideoRemove() {
  videoResult.value = null
}

function handleCommonSuccess(response: any) {
  commonResult.value = response
}

function handleCommonRemove() {
  commonResult.value = null
}
</script>

<template>
  <div class="upload-demo-container">
    <ElCard class="demo-card" header="音频上传组件演示">
      <AudioUpload
        @on-success="handleAudioSuccess"
        @on-remove="handleAudioRemove"
      />
      <div v-if="audioResult" class="demo-result">
        <h4>上传结果:</h4>
        <pre>{{ audioResult }}</pre>
      </div>
    </ElCard>

    <ElCard class="demo-card" header="视频上传组件演示">
      <VideoUpload
        @on-success="handleVideoSuccess"
        @on-remove="handleVideoRemove"
      />
      <div v-if="videoResult" class="demo-result">
        <h4>上传结果:</h4>
        <pre>{{ videoResult }}</pre>
      </div>
    </ElCard>

    <ElCard class="demo-card" header="通用上传组件演示">
      <CommonUpload
        upload-text="点击上传文件"
        accept=".pdf,.doc,.docx,.txt,.mp4,.mp3,.png,.jpg,.jpeg"
        @on-success="handleCommonSuccess"
        @on-remove="handleCommonRemove"
      />
      <div v-if="commonResult" class="demo-result">
        <h4>上传结果:</h4>
        <pre>{{ commonResult }}</pre>
      </div>
    </ElCard>
  </div>
</template>

<style scoped lang="scss">
.upload-demo-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
}

.demo-card {
  width: 100%;
}

.demo-result {
  padding: 10px;
  margin-top: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;

  pre {
    word-wrap: break-word;
    white-space: pre-wrap;
  }
}
</style>
