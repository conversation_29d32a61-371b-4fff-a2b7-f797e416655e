<route lang="yaml">
meta:
  enabled: false
</route>

<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { toast } from 'vue-sonner'

const { text, copy, copied } = useClipboard()

/* eslint-disable regexp/no-super-linear-backtracking, regexp/no-dupe-disjunctions, regexp/optimal-quantifier-concatenation, regexp/no-useless-assertions, regexp/no-contradiction-with-assertion, regexp/no-misleading-capturing-group */
const rules = ref([
  {
    title: '火车车次',
    rule: /^[GCDZTSPKXLY1-9]\d{1,4}$/,
    examples: ['G1868', 'D102', 'D9', 'Z5', 'Z24', 'Z17'],
  },
  {
    title: '手机机身码(IMEI)',
    rule: /^\d{15,17}$/,
    examples: ['123456789012345', '1234567890123456', '12345678901234567'],
  },
  {
    title: '必须带端口号的网址(或ip)',
    rule: /^((ht|f)tps?:\/\/)?[\w-]+(\.[\w-]+)+:\d{1,5}\/?$/,
    examples: ['https://www.qq.com:8080', '127.0.0.1:5050', 'baidu.com:8001', 'http://***********:9090'],
    counterExamples: ['***********', 'https://www.jd.com'],
  },
  {
    // 参考:
    // https://baike.baidu.com/item/%E9%A1%B6%E7%BA%A7%E5%9F%9F%E5%90%8D#4_1
    // https://baike.baidu.com/item/%E9%A1%B6%E7%BA%A7%E5%9F%9F%E5%90%8D#7
    // 也参考谷歌浏览器的地址栏, 如果输入非字母不会被识别为域名
    title: '网址(URL)',
    rule: /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{1,64})?\.)+[a-z]{2,6}\/?/,
    examples: ['www.qq.com', 'https://vuejs.org/v2/api/#v-model', 'www.qq.99', '//www.qq.com', 'www.腾讯.cs', 'ftp://baidu.qq', 'http://baidu.com', 'https://www.amap.com/search?id=BV10060895&city=420111&geoobj=113.207951%7C29.992557%7C115.785782%7C31.204369&query_type=IDQ&query=%E5%85%89%E8%B0%B7%E5%B9%BF%E5%9C%BA(%E5%9C%B0%E9%93%81%E7%AB%99)&zoom=10.15', '360.com:8080/vue/#/a=1&b=2'],
    counterExamples: ['....'],
  },
  {
    title: '统一社会信用代码',
    rule: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
    examples: ['91230184MA1BUFLT44', '92371000MA3MXH0E3W'],
  },
  {
    title: '统一社会信用代码(宽松匹配)(15位/18位/20位数字/字母)',
    rule: /^(([0-9A-Z]{15})|([0-9A-Z]{18})|([0-9A-Z]{20}))$/i,
    examples: ['91110108772551611J', '911101085923662400'],
  },
  {
    title: '迅雷链接',
    rule: /^thunderx?:\/\/[a-zA-Z\d]+=$/,
    examples: ['thunder://QUEsICdtYWduZXQ6P3h0PXVybjpidGloOjBCQTE0RTUxRkUwNjU1RjE0Qzc4NjE4RjY4NDY0QjZFNTEyNjcyOUMnWlo='],
  },

  {
    title: 'ed2k链接(宽松匹配)',
    rule: /^ed2k:\/\/\|file\|.+\|\/$/,
    examples: ['ed2k://|file|%E5%AF%84%E7%94%9F%E8%99%AB.PARASITE.2019.HD-1080p.X264.AAC-UUMp4(ED2000.COM).mp4|2501554832|C0B93E0879C6071CBED732C20CE577A3|h=5HTKZPQFYRKORN52I3M7GQ4QQCIHFIBV|/'],
  },

  {
    title: '磁力链接(宽松匹配)',
    rule: /^magnet:\?xt=urn:btih:[0-9a-fA-F]{40}.*$/,
    examples: ['magnet:?xt=urn:btih:40A89A6F4FB1498A98087109D012A9A851FBE0FC'],
  },
  {
    title: '子网掩码(不包含 0.0.0.0)',
    rule: /^(254|252|248|240|224|192|128)\.0\.0\.0|255\.(254|252|248|240|224|192|128|0)\.0\.0|255\.255\.(254|252|248|240|224|192|128|0)\.0|255\.255\.255\.(255|254|252|248|240|224|192|128|0)$/,
    examples: ['255.255.255.0', '255.255.255.255', '255.240.0.0'],
  },
  {
    title: 'linux"隐藏文件"路径',
    rule: /^\/(?:[^/]+\/)*\.[^/]*/,
    examples: ['/usr/ad/.dd', '/root/.gitignore', '/.gitignore'],
  },
  {
    title: 'linux文件夹路径',
    rule: /^\/(?:[^/]+\/)*$/,
    examples: ['/usr/ad/dd/', '/', '/root/', '/ a a / a / a a /'],
  },
  {
    title: 'linux文件路径',
    rule: /^\/(?:[^/]+\/)*[^/]+$/,
    examples: ['/root/b.ts', '/root/abc'],
  },
  {
    title: 'window"文件夹"路径',
    rule: /^[a-z]:\\(?:\w+\\?)*$/i,
    examples: ['C:\\Users\\<USER>\\Desktop', 'e:\\m\\'],
  },
  {
    title: 'window下"文件"路径',
    rule: /^[a-z]:\\(?:\w+\\)*\w+\.\w+$/i,
    examples: ['C:\\Users\\<USER>\\Desktop\\qq.link', 'e:\\m\\vscode.exe'],
  },
  {
    title: '股票代码(A股)',
    rule: /^(s[hz]|S[HZ])(000\d{3}|002\d{3}|300\d{3}|600\d{3}|60\d{4})$/,
    examples: ['sz000858', 'SZ002136', 'sz300675', 'SH600600', 'sh601155'],
  },
  {
    title: '大于等于0, 小于等于150, 支持小数位出现5, 如145.5, 用于判断考卷分数',
    rule: /^150$|^(?:\d|[1-9]\d|1[0-4]\d)(?:\.5)?$/,
    examples: [150, 100.5],
  },
  {
    title: 'html注释',
    rule: /<!--[\s\S]*?-->/g,
    examples: ['<!--<div class="_bubble"></div>--><div>chenguzhen87</div><div class="_bubble"></div>-->'],
  },
  {
    title: 'md5格式(32位)',
    rule: /^[a-f0-9]{32}$/i,
    examples: ['21fe181c5bfc16306a6828c1f7b762e8'],
  },
  {
    title: 'GUID/UUID',
    rule: /^[a-f\d]{4}(?:[a-f\d]{4}-){4}[a-f\d]{12}$/i,
    examples: ['e155518c-ca1b-443c-9be9-fe90fdab7345', '41E3DAF5-6E37-4BCC-9F8E-0D9521E2AA8D', '00000000-0000-0000-0000-000000000000'],
  },
  {
    title: '版本号(version)格式必须为X.Y.Z',
    rule: /^\d+(?:\.\d+){2}$/,
    examples: ['16.3.10'],
  },
  {
    title: '视频(video)链接地址（视频格式可按需增删）',
    rule: /^https?:\/\/(.+\/)+.+(\.(swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|mp4))$/i,
    examples: ['http://www.abc.com/video/wc.avi'],
  },
  {
    title: '图片(image)链接地址（图片格式可按需增删）',
    rule: /^https?:\/\/(.+\/)+.+(\.(gif|png|jpg|jpeg|webp|svg|psd|bmp|tif))$/i,
    examples: ['https://www.abc.com/logo.png', 'http://www.abc.com/logo.png'],
  },
  {
    title: '24小时制时间（HH:mm:ss）',
    rule: /^(?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/,
    examples: ['23:34:55'],
  },
  {
    title: '12小时制时间（hh:mm:ss）',
    rule: /^(?:1[0-2]|0?[1-9]):[0-5]\d:[0-5]\d$/,
    examples: ['11:34:55'],
    counterExamples: ['23:34:55'],
  },
  {
    title: 'base64格式',
    rule: /^\s*data:(?:[a-z]+\/[a-z0-9-+.]+(?:;[a-z-]+=[a-z0-9-]+)?)?(?:;base64)?,([\w!$&',()*+;=\-.~:@/?%\s]*?)\s*$/i,
    examples: ['data:image/gif;base64,xxxx=='],
  },
  {
    title: '数字/货币金额（支持负数、千分位分隔符）',
    rule: /^-?\d{1,3}(,\d{3})*(\.\d{1,2})?$/,
    examples: [100, -0.99, 3, 234.32, -1, 900, 235.09, '12,345,678.90'],
  },
  {
    title: '银行卡号（10到30位, 覆盖对公/私账户, 参考[微信支付](https://pay.weixin.qq.com/wiki/doc/api/xiaowei.php?chapter=22_1)）',
    rule: /^[1-9]\d{9,29}$/,
    examples: ['6234567890', '6222026006705354217'],
  },
  {
    title: '中文姓名',
    rule: /^[\u4E00-\u9FA5·]{2,16}$/,
    examples: ['葛二蛋', '凯文·杜兰特', '德克·维尔纳·诺维茨基'],
  },
  {
    title: '英文姓名',
    rule: /(^[a-z][a-z\s]{0,20}[a-z]$)/i,
    examples: ['James', 'Kevin Wayne Durant', 'Dirk Nowitzki'],
  },
  {
    title: '车牌号(新能源)',
    rule: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z](([DF]((?![IO])[a-zA-Z0-9](?![IO]))\d{4})|(\d{5}[DF]))$/,
    examples: ['京AD92035', '甘G23459F', '京A19203D'],
  },
  {
    title: '车牌号(非新能源)',
    rule: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]$/,
    examples: ['京A00599', '黑D23908'],
  },
  {
    title: '车牌号(新能源+非新能源)',
    rule: /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/,
    examples: ['京A12345D', '京A00599', '京AD92035', '甘G23459F', '京AA92035'],
    counterExamples: ['宁AD1234555555', '浙苏H6F681'],
  },
  {
    title: '手机号(mobile phone)中国(严谨), 根据工信部最新公布的手机号段',
    rule: /^(?:(?:\+|00)86)?1(?:3\d|4[5-79]|5[0-35-9]|6[5-7]|7[0-8]|8\d|9[0125-9])\d{8}$/,
    examples: ['008618311006933', '+8617888829981', '19119255642', '19519255642'],
  },
  {
    title: '手机号(mobile phone)中国(宽松), 只要是13,14,15,16,17,18,19开头即可',
    rule: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
    examples: ['008618311006933', '+8617888829981', '19119255642'],
  },
  {
    title: '手机号(mobile phone)中国(最宽松), 只要是1开头即可, 如果你的手机号是用来接收短信, 优先建议选择这一条',
    rule: /^(?:(?:\+|00)86)?1\d{10}$/,
    examples: ['008618311006933', '+8617888829981', '19119255642'],
  },
  {
    title: '日期(宽松)',
    rule: /^\d{1,4}(-)(1[0-2]|0?[1-9])\1(0?[1-9]|[12]\d|30|31)$/,
    examples: ['1990-12-12', '1-1-1', '0000-1-1'],
    counterExamples: ['2020-00-01'],
  },

  {
    title: '日期(严谨, 支持闰年判断)',
    rule: /^((\d{3}[1-9]|\d{2}[1-9]\d|\d[1-9]\d{2}|[1-9]\d{3})-(((0[13578]|1[02])-(0[1-9]|[12]\d|3[01]))|((0[469]|11)-(0[1-9]|[12]\d|30))|(02-(0[1-9]|1\d|2[0-8]))))|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))-02-29)$/,
    examples: ['1990-12-12', '2000-02-29'],
    counterExamples: ['2021-02-29'],
  },

  {
    title: '中国省',
    rule: /^浙江|上海|北京|天津|重庆|黑龙江|吉林|辽宁|内蒙古|河北|新疆|甘肃|青海|陕西|宁夏|河南|山东|山西|安徽|湖北|湖南|江苏|四川|贵州|云南|广西|西藏|江西|广东|福建|台湾|海南|香港|澳门$/,
    examples: ['浙江', '台湾'],
    counterExamples: ['哈尔滨'],
  },

  {
    title: '可以被moment转化成功的时间 YYYYMMDD HH:mm:ss',
    rule: /^\d{4}(\S)(1[0-2]|0?[1-9])\1(0?[1-9]|[12]\d|30|31) (?:[01]\d|2[0-3]):[0-5]\d:[0-5]\d$/,
    examples: ['2020/01/01 23:59:59', '2020-01-01 00:00:00', '20200101 11:11:11'],
    counterExamples: ['2020/00/01 23:59:59', '2020-01/01 23:59:59', '2020-01-01 23:59:61', '2020-01-0100:00:00'],
  },
  {
    title: 'email(邮箱)',
    rule: /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\])|(([a-z\-0-9]+\.)+[a-z]{2,}))$/i,
    examples: ['<EMAIL>', '<EMAIL>', '汉字@qq.com'],
  },

  {
    title: '座机(tel phone)电话(国内),如: 0341-86091234',
    rule: /^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/,
    examples: ['0936-4211235', '89076543', '010-12345678-1234'],
  },

  {
    title: '身份证号(1代,15位数字)',
    rule: /^[1-9]\d{7}(?:0\d|10|11|12)(?:0[1-9]|[12]\d|30|31)\d{3}$/,
    examples: ['123456991010193'],
  },
  {
    title: '身份证号(2代,18位数字),最后一位是校验位,可能为数字或字符X',
    rule: /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[12]\d|30|31)\d{3}[\dX]$/i,
    examples: ['12345619991205131x'],
  },
  {
    title: '身份证号, 支持1/2代(15位/18位数字)',
    rule: /^\d{6}((((((19|20)\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|(((19|20)\d{2})(0[13578]|1[02])31)|((19|20)\d{2})02(0[1-9]|1\d|2[0-8])|((((19|20)([13579][26]|[2468][048]|0[48]))|(2000))0229))\d{3})|((((\d{2})(0[13-9]|1[012])(0[1-9]|[12]\d|30))|((\d{2})(0[13578]|1[02])31)|((\d{2})02(0[1-9]|1\d|2[0-8]))|(([13579][26]|[2468][048]|0[048])0229))\d{2}))([\dX])$/i,
    examples: ['622223199912051311', '12345619991205131x', '123456991010193'],
  },
  {
    title: '护照（包含香港、澳门）',
    rule: /(^[EKGDSPH]\d{8}$)|(^((E[a-f])|([DSP]E)|(KJ)|(MA)|(1[45]))\d{7}$)/i,
    examples: ['s28233515', '141234567', '159203084', 'MA1234567', 'K25345719'],
  },
  {
    title: '帐号是否合法(字母开头，允许5-16字节，允许字母数字下划线组合',
    rule: /^[a-z]\w{4,15}$/i,
    examples: ['justin', 'justin1989', 'justin_666'],
  },
  {
    title: '中文/汉字',
    // rule: /^[\u4E00-\u9FA5]+$/,
    rule: /^(?:[\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0])+$/,
    examples: ['正则', '前端'],
  },
  {
    title: '小数(支持科学计数)',
    rule: /^[+-]?(\d+(\.\d*)?(e[+-]?\d+)?|\.\d+(e[+-]?\d+)?)$/i,
    examples: ['0.0', '0.09', '4E+4'],
  },
  {
    title: '只包含数字',
    rule: /^\d+$/,
    examples: [12345678],
  },
  {
    title: 'html标签(宽松匹配)',
    rule: /<(\w+)[^>]*>(.*?<\/\1>)?/,
    examples: ['<div id="app"> 2333 </div>', '<input type="text">', '<br>'],
  },

  {
    title: '匹配中文汉字和中文标点',
    rule: /[\u4E00-\u9FA5|\u3002\uFF1F\uFF01\uFF0C\u3001\uFF1B\uFF1A\u201C\u201D\u2018\u2019\uFF08\uFF09\u300A\u300B\u3008\u3009\u3010\u3011\u300E\u300F\u300C\u300D\uFE43\uFE44\u3014\u3015\u2026\u2014\uFF5E\uFE4F\uFFE5]/,
    examples: ['匹配中文汉字以及中文标点符号 。 ？ ！ ， 、 ； ： “ ” ‘ \' （ ） 《 》 〈 〉 【 】 『 』 「 」 ﹃ ﹄ 〔 〕 … — ～ ﹏ ￥'],
  },

  {
    title: 'qq号格式正确',
    rule: /^[1-9]\d{4,10}$/,
    examples: [903013545, 9020304],
  },
  {
    title: '数字和字母组成',
    rule: /^[A-Z0-9]+$/i,
    examples: ['james666', 'haha233hi'],
  },
  {
    title: '英文字母',
    rule: /^[a-z]+$/i,
    examples: ['Russel'],
  },
  {
    title: '小写英文字母组成',
    rule: /^[a-z]+$/,
    examples: ['russel'],
  },
  {
    title: '大写英文字母',
    rule: /^[A-Z]+$/,
    examples: ['ABC', 'KD'],
  },
  {
    title: '密码强度校验，最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符',
    rule: /^\S*(?=\S{6})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])(?=\S*[!@#$%^&*? ])\S*$/,
    examples: ['Kd@curry666'],
  },
  {
    title: '用户名校验，4到16位（字母，数字，下划线，减号）',
    rule: /^[\w-]{4,16}$/,
    examples: ['xiaohua_qq'],
  },
  {
    title: 'ip-v4[:端口]',
    rule: /^((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:\d|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))?$/,
    examples: ['**********', '**********:8080', '*********', '*********:998'],
  },
  {
    title: '16进制颜色',
    rule: /^#?([a-f0-9]{6}|[a-f0-9]{3}|[a-f0-9]{8}|[a-f0-9]{4})$/i,
    examples: ['#f00', '#F90', '#000', '#fe9de8', '#f8f8f8ff', '#f003'],
  },
  {
    title: '微信号(wx)，6至20位，以字母开头，字母，数字，减号，下划线',
    rule: /^[a-z][-\w]{5,19}$/i,
    examples: ['github666', 'kd_-666'],
  },
  {
    title: '邮政编码(中国)',
    rule: /^(0[1-7]|1[0-356]|2[0-7]|3[0-6]|4[0-7]|5[1-7]|6[1-7]|7[0-5]|8[013-6])\d{4}$/,
    examples: ['734500', '100101'],
  },
  {
    title: '中文和数字',
    rule: /^([\u3400-\u4DB5\u4E00-\u9FEA\uFA0E\uFA0F\uFA11\uFA13\uFA14\uFA1F\uFA21\uFA23\uFA24\uFA27-\uFA29]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|(\d))+$/,
    examples: ['哈哈哈', '你好6啊'],
  },
  {
    title: '不能包含字母',
    rule: /^[^A-Z]*$/i,
    examples: ['你好6啊', '@¥()！'],
  },
  {
    title: 'java包名',
    rule: /^([a-z_]\w*)+(\.[a-z_]\w*)+$/i,
    examples: ['com.bbb.name'],
  },
  {
    title: 'mac地址',
    rule: /^(([a-f0-9][0,2468ace]:([a-f0-9]{2}:){4})|([a-f0-9][0,2468ace]-([a-f0-9]{2}-){4}))[a-f0-9]{2}$/i,
    examples: ['38:f9:d3:4b:f5:51', '00-0C-29-CA-E4-66'],
  },
  {
    title: '匹配连续重复的字符',
    rule: /(.)\1+/,
    examples: ['我我我', '112233', '11234'],
  },
  {
    title: '数字和英文字母组成，并且同时含有数字和英文字母',
    rule: /^(?=.*[a-z])(?=.*\d).+$/i,
    examples: ['我a我1我', 'a对1'],
  },
  {
    title: '香港身份证 ',
    rule: /^[a-zA-Z]\d{6}\([\dA]\)$/,
    examples: ['K034169(1)'],
  },
  {
    // 参考:
    // https://baike.baidu.com/item/%E6%BE%B3%E9%97%A8%E5%B1%85%E6%B0%91%E8%BA%AB%E4%BB%BD%E8%AF%81/12509098?fr=aladdin#5
    title: '澳门身份证 ',
    rule: /^[1|57]\d{6}\(\d\)$/,
    examples: ['5686611(1)'],
  },
  {
    title: '台湾身份证 ',
    rule: /^[a-z]\d{9}$/i,
    examples: ['U193683453'],
  },
  {
    title: '大写字母，小写字母，数字，特殊符号 `@#$%^&*`~()-+=` 中任意3项密码',
    rule: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[\s\S]/,
    examples: ['a1@', 'A1@', 'Aa@'],
  },
  {
    title: 'ASCII码表中的全部的特殊字符',
    rule: /[\x21-\x2F\x3A-\x40\x5B-\x60\x7B-\x7E]+/,
    examples: ['[', '.', '^', '&3%'],
  },
  {
    title: '正整数，不包含0',
    rule: /^\+?[1-9]\d*$/,
    examples: [1231],
  },
  {
    title: '负整数，不包含0',
    rule: /^-[1-9]\d*$/,
    examples: [-1231],
  },
  {
    title: '整数',
    rule: /^(?:0|-?[1-9]\d*)$/,
    examples: [-1231, 123, 0],
    counterExamples: ['01'],
  },
  {
    title: '浮点数',
    rule: /^(-?[1-9]\d*\.\d+|-?0\.0*[1-9]\d*|0\.0+)$/,
    examples: ['1.23', '-1.01', '0.00'],
    // allow "1.23", allow "-0.1", allow "0.00", ban "-0.00", ban "2.", allow "2.0"
  },
  {
    title: '浮点数(严格)',
    rule: /^(-?[1-9]\d*\.\d+|-?0\.\d*[1-9])$/,
    examples: ['1.23', '-1.01'],
    // allow "1.23", allow "-0.1", ban "2.", ban "2.0"
  },
  {
    title: 'email(支持中文邮箱)',
    rule: /^[A-Z0-9\u4E00-\u9FA5]+@[\w-]+(\.[\w-]+)+$/i,
    examples: ['<EMAIL>', '<EMAIL>', '啦啦啦@126.com'],
  },

  {
    title: '域名(非网址, 不包含协议)',
    rule: /^([0-9a-z-]+\.)+([a-z]{2,})$/i,
    examples: ['www.baidu.com', 'baidu.com', 'baidu.com.cn', 'api.baidu.com', 'nodejs.org', 'nodejs.cn'],
    counterExamples: ['http://baidu.com', 'https://baidu.com', 'www.百度.com'],
  },

  {
    title: '军官/士兵证',
    rule: /^[\u4E00-\u9FA5](字第)([0-9a-z]{4,8})(号?)$/i,
    examples: ['军字第2001988号', '士字第P011816X号'],
  },
  {
    title: '户口薄',
    rule: /(^\d{15}$)|(^\d{18}$)|(^\d{17}([\dX])$)/i,
    examples: ['441421999707223115'],
  },
])

const dialog = ref({
  visible: false,
  index: 0,
  form: {
    test: '',
  },
  formRules: {
    test: [
      {
        validator: (_rule: any, value: any, callback: any) => {
          if (rules.value[dialog.value.index].rule.test(value)) {
            callback()
          }
          else {
            callback(new Error('不通过'))
          }
        },
        trigger: 'change',
      },
    ],
  },
})

watch(copied, (val) => {
  val && toast.success(`复制成功：${text.value}`)
})

function test(index: number) {
  dialog.value.index = index
  dialog.value.form.test = ''
  dialog.value.visible = true
}
function open(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <div>
    <FaPageHeader title="常用正则" description="正则来源于 Github 上 any-rule 项目">
      <FaButton variant="outline" @click="open('https://github.com/any86/any-rule')">
        <FaIcon name="i-ep:link" />
        访问 any-rule
      </FaButton>
    </FaPageHeader>
    <FaPageMain v-for="(item, index) in rules" :key="index" :title="item.title">
      <div class="rule">
        {{ item.rule }}
      </div>
      <div class="space-x-2">
        <FaButton @click="copy(item.rule.toString())">
          复制
        </FaButton>
        <FaButton variant="outline" @click="test(index)">
          测试
        </FaButton>
      </div>
    </FaPageMain>
    <ElDialog v-model="dialog.visible" :title="rules[dialog.index].title" width="500px">
      <ElForm :model="dialog.form" :rules="dialog.formRules">
        <ElFormItem prop="test">
          <ElInput v-model="dialog.form.test" :placeholder="`例如：${rules[dialog.index].examples.join('、')}`" />
        </ElFormItem>
      </ElForm>
    </ElDialog>
  </div>
</template>

<style scoped>
.rule {
  margin-bottom: 20px;
}
</style>
