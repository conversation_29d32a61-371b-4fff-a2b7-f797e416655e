<route lang="yaml">
meta:
  title: 用户管理
  icon: i-ri:user-line
  # auth: '/system/account/query'
</route>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { computed, nextTick, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import apiSystem from '@/api/modules/system'
import DepartmentSelector from '@/components/DepartmentSelector/index.vue'

defineOptions({
  name: 'SystemUsers',
})

// 用户数据接口
interface User {
  id: string
  name: string
  account: string
  available: boolean
  ctime: number
  roles: Array<{
    id: string
    name: string
    ctime: number
  }>
}

// 角色数据接口
interface Role {
  id: string
  name: string
  ctime: number
}

// 部门数据接口
interface Department {
  id: string
  name: string
  parentId?: string
  sort?: number
  ctime: number
  children?: Department[]
}

// 响应式数据
const loading = ref(false)
const userList = ref<User[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索条件
const searchForm = ref({
  name: '',
  account: '',
  partmentId: '',
  available: '' as string | boolean,
})

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增用户')
const dialogLoading = ref(false)
const isEdit = ref(false)

// 表单数据
const formData = ref({
  id: '',
  name: '',
  account: '',
  roleIds: [] as string[],
  partmentId: '',
  available: true,
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入用户名称', trigger: 'blur' },
    { min: 2, max: 50, message: '用户名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
  account: [
    { required: true, message: '请输入用户账号', trigger: 'blur' },
    { min: 2, max: 50, message: '用户账号长度在 2 到 50 个字符', trigger: 'blur' },
  ],
}

// 表单引用
const formRef = ref()
const nameInputRef = ref()

// 角色列表
const roleList = ref<Role[]>([])
const roleLoading = ref(false)

// 部门树数据
const departmentTreeData = ref<Department[]>([])
const departmentTreeLoading = ref(false)

// 重置密码对话框
const resetPasswordDialogVisible = ref(false)
const resetPasswordLoading = ref(false)
const resetPasswordForm = ref({
  id: '',
  password: '',
  confirmPassword: '',
})
const resetPasswordFormRef = ref()

// 重置密码表单验证规则
const resetPasswordRules = {
  password: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' },
  ],
  confirmPassword: [
    { required: true, message: '请再次输入新密码', trigger: 'blur' },
    {
      validator: (_rule: any, value: string, callback: any) => {
        if (value !== resetPasswordForm.value.password) {
          callback(new Error('两次输入的密码不一致'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
}

// 可用状态选项
const availableOptions: { label: string, value: string | boolean }[] = [
  { label: '全部', value: '' },
  { label: '可用', value: true },
  { label: '禁用', value: false },
]

// 监听对话框关闭，清理状态
function handleDialogClose() {
  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    account: '',
    roleIds: [],
    partmentId: '',
    available: true,
  }

  // 重置编辑状态
  isEdit.value = false
  dialogTitle.value = '新增用户'
}

// 获取用户列表
async function getUserList() {
  loading.value = true
  try {
    const res: any = await apiSystem.getUserList({
      page: currentPage.value - 1,
      size: pageSize.value,
      partmentId: searchForm.value.partmentId || undefined,
      name: searchForm.value.name || undefined,
      account: searchForm.value.account || undefined,
      available: searchForm.value.available === '' ? undefined : searchForm.value.available as boolean,
    })
    if (res.code === 0) {
      userList.value = res.data.content || []
      total.value = res.data.total || 0
    }
    else {
      toast.error(res.msg || '获取用户列表失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取用户列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取角色列表
async function getRoleList() {
  roleLoading.value = true
  try {
    const res: any = await apiSystem.getRoleList({
      page: 0,
      size: 1000, // 获取所有角色
    })
    if (res.code === 0) {
      roleList.value = res.data.content || []
    }
    else {
      toast.error(res.msg || '获取角色列表失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取角色列表失败')
  }
  finally {
    roleLoading.value = false
  }
}

// 获取部门树数据
async function getDepartmentTree() {
  departmentTreeLoading.value = true
  try {
    const res: any = await apiSystem.getDepartmentList({
      page: 0,
      size: 1000, // 获取所有部门用于构建树
    })
    if (res.code === 0) {
      // 将扁平数据转换为树形结构
      departmentTreeData.value = buildDepartmentTree(res.data.content || [])
    }
    else {
      toast.error(res.msg || '获取部门数据失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取部门数据失败')
  }
  finally {
    departmentTreeLoading.value = false
  }
}

// 构建部门树结构
function buildDepartmentTree(departmentList: Department[]): Department[] {
  const departmentMap = new Map<string, Department>()
  const rootDepartments: Department[] = []

  // 创建部门映射
  departmentList.forEach((department) => {
    departmentMap.set(department.id, { ...department, children: [] })
  })

  // 构建树形结构
  departmentList.forEach((department) => {
    const departmentNode = departmentMap.get(department.id)!
    if (department.parentId && departmentMap.has(department.parentId)) {
      const parentNode = departmentMap.get(department.parentId)!
      parentNode.children!.push(departmentNode)
    }
    else {
      rootDepartments.push(departmentNode)
    }
  })

  return rootDepartments
}

// 打开新增对话框
function openAddDialog() {
  isEdit.value = false
  dialogTitle.value = '新增用户'

  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    account: '',
    roleIds: [],
    partmentId: '',
    available: true,
  }

  // 打开对话框
  dialogVisible.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}

// 打开编辑对话框
async function openEditDialog(row: User) {
  isEdit.value = true
  dialogTitle.value = '编辑用户'

  // 设置表单数据
  formData.value = {
    id: row.id,
    name: row.name,
    account: row.account,
    roleIds: row.roles.map(role => role.id),
    partmentId: '', // 部门信息需要从用户详情获取
    available: row.available,
  }

  // 打开对话框
  dialogVisible.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}

// 保存用户
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    // 表单验证
    await formRef.value.validate()

    dialogLoading.value = true

    let res: any
    if (isEdit.value) {
      // 编辑用户
      res = await apiSystem.saveUser(formData.value)
    }
    else {
      // 新增用户
      const { id, ...addData } = formData.value
      res = await apiSystem.saveUser(addData)
    }

    if (res.code === 0) {
      toast.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getUserList()
    }
    else {
      // toast.error(res.msg || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  catch (error: any) {
    if (error !== false) { // 表单验证失败不显示错误
      // toast.error(error.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  finally {
    dialogLoading.value = false
  }
}

// 删除用户
async function handleDelete(row: User) {
  try {
    await ElMessageBox.confirm(
      `确定要删除用户"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const res: any = await apiSystem.deleteUser({ id: row.id })
    if (res.code === 0) {
      toast.success('删除成功')
      getUserList()
    }
    else {
      toast.error(res.msg || '删除失败')
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      toast.error(error.message || '删除失败')
    }
  }
}

// 打开重置密码对话框
function openResetPasswordDialog(row: User) {
  resetPasswordForm.value = {
    id: row.id,
    password: '',
    confirmPassword: '',
  }
  resetPasswordDialogVisible.value = true
}

// 重置密码
async function handleResetPassword() {
  if (!resetPasswordFormRef.value) {
    return
  }

  try {
    // 表单验证
    await resetPasswordFormRef.value.validate()

    resetPasswordLoading.value = true

    // MD5加密密码
    const md5Password = btoa(resetPasswordForm.value.password) // 简单base64编码，实际项目应使用MD5

    const res: any = await apiSystem.resetUserPassword({
      id: resetPasswordForm.value.id,
      password: md5Password,
    })

    if (res.code === 0) {
      toast.success('重置密码成功')
      resetPasswordDialogVisible.value = false
    }
    else {
      toast.error(res.msg || '重置密码失败')
    }
  }
  catch (error: any) {
    if (error !== false) { // 表单验证失败不显示错误
      toast.error(error.message || '重置密码失败')
    }
  }
  finally {
    resetPasswordLoading.value = false
  }
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  getUserList()
}

// 重置搜索
function handleReset() {
  searchForm.value = {
    name: '',
    account: '',
    partmentId: '',
    available: '',
  }
  currentPage.value = 1
  getUserList()
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  getUserList()
}

// 页面大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  getUserList()
}

// 格式化角色名称
const formatRoleNames = computed(() => {
  return (roles: Array<{ name: string }>) => {
    return roles.map(role => role.name).join(', ') || '-'
  }
})

// 初始化
onMounted(() => {
  getUserList()
  getRoleList()
  getDepartmentTree()
})
</script>

<template>
  <div>
    <FaPageHeader title="用户管理" description="管理系统用户账号" />

    <FaPageMain>
      <!-- 搜索表单 -->
      <el-form :model="searchForm" inline>
        <el-form-item label="用户名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入用户名称"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="用户账号">
          <el-input
            v-model="searchForm.account"
            placeholder="请输入用户账号"
            clearable
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="所属部门">
          <DepartmentSelector
            v-model="searchForm.partmentId"
            :department-data="departmentTreeData"
            placeholder="请选择所属部门"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.available"
            placeholder="请选择状态"
            clearable
            style="width: 120px;"
          >
            <el-option
              v-for="item in availableOptions"
              :key="String(item.value)"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <ElButton type="primary" @click="handleSearch">
            <template #icon>
              <FaIcon name="i-ep:search" />
            </template>
            搜索
          </ElButton>
          <ElButton class="ml-2" @click="handleReset">
            <template #icon>
              <FaIcon name="i-ep:refresh" />
            </template>
            重置
          </ElButton>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-2">
          <FaAuth :value="['/system/account/save']">
            <FaButton
              type="primary"
              icon="i-ri:add-line"
              @click="openAddDialog"
            >
              新增用户
            </FaButton>
          </FaAuth>
          <FaButton
            type="success"
            icon="i-ri:refresh-line"
            @click="getUserList"
          >
            刷新
          </FaButton>
        </div>
      </div>

      <!-- 用户列表 -->
      <el-table
        v-loading="loading"
        :data="userList"
        style="width: 100%;"
        border
      >
        <el-table-column prop="name" label="用户名称" min-width="120">
          <template #default="scope">
            <span class="font-medium">{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="account" label="用户账号" min-width="150">
          <template #default="scope">
            <span class="text-blue-600">{{ scope.row.account }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="roles" label="角色" min-width="150">
          <template #default="scope">
            <span class="text-gray-600">
              {{ formatRoleNames(scope.row.roles) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column prop="available" label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.available ? 'success' : 'danger'" size="small">
              {{ scope.row.available ? '可用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            <span class="text-gray-500">
              {{ new Date(scope.row.ctime).toLocaleString() }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="center">
          <template #default="scope">
            <FaAuth :value="['/system/account/save']">
              <el-button
                text
                type="primary"
                size="small"
                @click="openEditDialog(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:edit" />
                </template>
                编辑
              </el-button>
            </FaAuth>
            <FaAuth :value="['/system/account/resetPassword']">
              <el-button
                text
                type="warning"
                size="small"
                @click="openResetPasswordDialog(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:key" />
                </template>
                重置密码
              </el-button>
            </FaAuth>
            <FaAuth :value="['/system/account/delete']">
              <el-button
                text
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:delete" />
                </template>
                删除
              </el-button>
            </FaAuth>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form ref="formRef" :model="formData" label-width="100px" :rules="formRules">
        <el-form-item label="用户名称" prop="name">
          <el-input
            ref="nameInputRef"
            v-model="formData.name"
            placeholder="请输入用户名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="用户账号" prop="account">
          <el-input
            v-model="formData.account"
            placeholder="请输入用户账号"
            maxlength="50"
            show-word-limit
            :disabled="isEdit"
          />
        </el-form-item>

        <el-form-item label="所属部门" prop="partmentId">
          <DepartmentSelector
            v-model="formData.partmentId"
            :department-data="departmentTreeData"
            placeholder="请选择所属部门"
          />
        </el-form-item>

        <el-form-item label="角色授权" prop="roleIds">
          <el-select
            v-model="formData.roleIds"
            multiple
            placeholder="请选择角色"
            style="width: 100%;"
          >
            <el-option
              v-for="role in roleList"
              :key="role.id"
              :label="role.name"
              :value="role.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="用户状态" prop="available">
          <el-radio-group v-model="formData.available">
            <el-radio :value="true">
              可用
            </el-radio>
            <el-radio :value="false">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="handleSave"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      v-model="resetPasswordDialogVisible"
      title="重置密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form ref="resetPasswordFormRef" :model="resetPasswordForm" label-width="100px" :rules="resetPasswordRules">
        <el-form-item label="新密码" prop="password">
          <el-input
            v-model="resetPasswordForm.password"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>

        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="resetPasswordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="resetPasswordLoading"
            @click="handleResetPassword"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
