<route lang="yaml">
meta:
  title: 场次管理
  icon: i-ri:calendar-schedule-line
  auth: /ticket/play/query
</route>

<script setup lang="ts">
import type { Schedule, ScheduleSeat } from '@/types/cinema'
import { onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import apiCinema from '@/api/modules/cinema'

defineOptions({
  name: 'CinemaSchedule',
})

// 使用导入的类型定义

// 响应式数据
const loading = ref(false)
const scheduleList = ref<Schedule[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 影院相关数据
const cinemaLoading = ref(false)
const cinemaList = ref<any[]>([])
const selectedCinema = ref('')

// 搜索条件
const searchForm = ref({
  cinemaCode: '',
})

// 获取影院列表
async function getCinemaList() {
  cinemaLoading.value = true
  try {
    const res: any = await apiCinema.getAllCinemaList()
    if (res.code === 0) {
      cinemaList.value = res.data || []
    }
    else {
      toast.error(res.msg || '获取影院列表失败')
      // 使用模拟数据
      cinemaList.value = [
        { cinemaCode: 'CINEMA001', cinemaName: '万达影城(CBD店)' },
        { cinemaCode: 'CINEMA002', cinemaName: '星美国际影城' },
        { cinemaCode: 'CINEMA003', cinemaName: '大地影院' },
      ]
    }
  }
  catch (error: any) {
    console.error('获取影院列表失败:', error)
    // 使用模拟数据
    cinemaList.value = [
      { cinemaCode: 'CINEMA001', cinemaName: '万达影城(CBD店)' },
      { cinemaCode: 'CINEMA002', cinemaName: '星美国际影城' },
      { cinemaCode: 'CINEMA003', cinemaName: '大地影院' },
    ]
  }
  finally {
    cinemaLoading.value = false
  }
}

// 获取场次列表
async function getScheduleList() {
  // 如果没有选择影院，不执行查询
  if (!selectedCinema.value) {
    scheduleList.value = []
    total.value = 0
    return
  }

  loading.value = true
  try {
    // API调用
    const res: any = await apiCinema.getScheduleList({
      page: currentPage.value - 1,
      size: pageSize.value,
      cinemaCode: selectedCinema.value,
    })

    if (res.code === 0) {
      scheduleList.value = res.data.content || []
      total.value = res.data.total || 0
    }
    else {
      toast.error(res.msg || '获取场次列表失败')
      // 使用模拟数据作为fallback
      useMockData()
    }
  }
  catch (error: any) {
    console.error('API调用失败，使用模拟数据:', error)
    // 当API调用失败时，使用模拟数据
    useMockData()
  }
  finally {
    loading.value = false
  }
}

// 使用模拟数据
function useMockData() {
  const mockData: Schedule[] = [
    {
      id: '1',
      code: 'SCHEDULE001',
      cinemaCode: 'CINEMA001',
      screenCode: 'SCREEN001',
      startTime: Date.now() + 3600000, // 1小时后
      playthroughFlag: 'No',
      marketingCode: 'PROMO001',
      marketingName: '春节档优惠',
      films: [
        {
          id: '1',
          code: 'FILM001',
          name: '阿凡达：水之道',
          lang: '中文',
          duration: 192,
          sequence: 1,
          version: '3D IMAX',
          publishDate: '2023-12-16',
          publisher: '20世纪影业',
          producer: '詹姆斯·卡梅隆',
          director: '詹姆斯·卡梅隆',
          cast: '萨姆·沃辛顿,佐伊·索尔达娜',
          introduction: '《阿凡达：水之道》是一部科幻冒险电影',
          provider: '20世纪影业',
          mtime: Date.now(),
        },
      ],
      price: {
        id: '1',
        lowestPrice: 3500, // 35元，以分为单位
        standardPrice: 4500, // 45元
        listingPrice: 5000, // 50元
        serviceAddFee: 300, // 3元
        cinemaAllowance: 500, // 5元
        mtime: Date.now(),
      },
      seats: [
        {
          id: '1',
          featureAppNo: 'SCHEDULE001',
          seatCode: 'A01',
          rowNum: 1,
          columnNum: 1,
          status: 'Available',
          memberLevelCode: 'AllUser',
          xCoord: 100,
          yCoord: 100,
          level: 1,
          levelName: '普通座',
          provider: '影院系统',
          mtime: Date.now(),
        },
      ],
      provider: '影院系统',
      ctime: Date.now() - 86400000,
      uptime: Date.now(),
    },
    {
      id: '2',
      code: 'SCHEDULE002',
      cinemaCode: 'CINEMA001',
      screenCode: 'SCREEN002',
      startTime: Date.now() + 7200000, // 2小时后
      playthroughFlag: 'Yes',
      marketingCode: 'PROMO002',
      marketingName: '连场优惠',
      films: [
        {
          id: '2',
          code: 'FILM002',
          name: '流浪地球2',
          lang: '中文',
          duration: 173,
          sequence: 1,
          version: '2D',
          publishDate: '2023-01-22',
          publisher: '中国电影',
          producer: '郭帆',
          director: '郭帆',
          cast: '吴京,刘德华,李雪健',
          introduction: '《流浪地球2》是一部中国科幻电影',
          provider: '中国电影',
          mtime: Date.now(),
        },
      ],
      price: {
        id: '2',
        lowestPrice: 3000,
        standardPrice: 4000,
        listingPrice: 4500,
        serviceAddFee: 300,
        cinemaAllowance: 400,
        mtime: Date.now(),
      },
      seats: [],
      provider: '影院系统',
      ctime: Date.now() - 172800000,
      uptime: Date.now(),
    },
  ]

  // 模拟搜索过滤
  let filteredData = mockData.filter(item => item.cinemaCode === selectedCinema.value)
  if (searchForm.value.cinemaCode) {
    filteredData = filteredData.filter(item =>
      item.cinemaCode.includes(searchForm.value.cinemaCode),
    )
  }

  scheduleList.value = filteredData
  total.value = filteredData.length
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  getScheduleList()
}

// 重置搜索
function handleReset() {
  searchForm.value = {
    cinemaCode: '',
  }
  handleSearch()
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  getScheduleList()
}

// 分页大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  getScheduleList()
}

// 影院选择变化
function handleCinemaChange() {
  currentPage.value = 1
  getScheduleList()
}

// 格式化时间
function formatDateTime(timestamp: number) {
  return new Date(timestamp).toLocaleString()
}

// 格式化价格（分转元）
function formatPrice(price: number) {
  return (price / 100).toFixed(2)
}

// 格式化连场标志
function formatPlaythroughFlag(flag: string) {
  return flag === 'Yes' ? '连场' : '非连场'
}

// 格式化座位状态
function formatSeatStatus(status: string) {
  const statusMap: Record<string, string> = {
    Available: '可出售',
    Locked: '已锁定',
    Sold: '已售出',
    Booked: '已预订',
    Unavailable: '不可用',
    Isolate: '隔离座',
  }
  return statusMap[status] || status
}

// 详情对话框相关
const detailDialogVisible = ref(false)
const currentSchedule = ref<Schedule | null>(null)

// 座位图对话框相关
const seatsDialogVisible = ref(false)
const currentSeats = ref<ScheduleSeat[]>([])

// 查看详情
function handleViewDetail(schedule: Schedule) {
  currentSchedule.value = schedule
  detailDialogVisible.value = true
}

// 查看座位图
function handleViewSeats(schedule: Schedule) {
  currentSeats.value = schedule.seats
  seatsDialogVisible.value = true
}

// 初始化
onMounted(() => {
  getCinemaList()
})
</script>

<template>
  <div>
    <FaPageHeader title="场次管理" description="管理影院的放映场次，包括排期、价格、座位等信息" />

    <FaPageMain>
      <!-- 影院选择区域 -->
      <div class="mb-6 border border-blue-200 rounded-lg bg-blue-50 p-4">
        <div class="flex items-center gap-4">
          <span class="text-sm text-blue-700 font-medium">请选择影院：</span>
          <el-select
            v-model="selectedCinema"
            placeholder="请选择影院"
            clearable
            filterable
            :loading="cinemaLoading"
            style="width: 300px;"
            @change="handleCinemaChange"
          >
            <el-option
              v-for="cinema in cinemaList"
              :key="cinema.cinemaCode"
              :label="`${cinema.cinemaName} (${cinema.cinemaCode})`"
              :value="cinema.cinemaCode"
            />
          </el-select>
          <el-text v-if="!selectedCinema" type="info" size="small">
            选择影院后将显示该影院的排片信息
          </el-text>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="mb-4 flex flex-wrap items-center gap-4">
        <el-input
          v-model="searchForm.cinemaCode"
          placeholder="影院编码"
          clearable
          style="width: 200px;"
          @keyup.enter="handleSearch"
        />

        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          重置
        </el-button>
      </div>

      <!-- 场次表格 -->
      <div v-if="!selectedCinema" class="py-12 text-center">
        <el-empty description="请先选择影院查看排片信息" />
      </div>
      <el-table
        v-else
        v-loading="loading"
        :data="scheduleList"
        stripe
        border
      >
        <el-table-column prop="code" label="场次编码" width="120" />
        <el-table-column prop="cinemaCode" label="影院编码" width="120" />
        <el-table-column prop="screenCode" label="影厅编码" width="120" />
        <el-table-column prop="startTime" label="开始时间" width="180">
          <template #default="scope">
            {{ formatDateTime(scope.row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="playthroughFlag" label="连场标志" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.playthroughFlag === 'Yes' ? 'success' : 'info'">
              {{ formatPlaythroughFlag(scope.row.playthroughFlag) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="marketingName" label="活动名称" width="120" show-overflow-tooltip />
        <el-table-column label="影片信息" min-width="200">
          <template #default="scope">
            <div v-if="scope.row.films.length > 0">
              <div v-for="film in scope.row.films" :key="film.id" class="mb-1">
                <div class="font-medium">
                  {{ film.name }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ film.version }} | {{ film.duration }}分钟 | {{ film.director }}
                </div>
              </div>
            </div>
            <span v-else class="text-gray-400">暂无影片</span>
          </template>
        </el-table-column>
        <el-table-column label="价格信息" width="150">
          <template #default="scope">
            <div class="text-sm">
              <div>标准价: ¥{{ formatPrice(scope.row.price.standardPrice) }}</div>
              <div>最低价: ¥{{ formatPrice(scope.row.price.lowestPrice) }}</div>
              <div>成人价: ¥{{ formatPrice(scope.row.price.listingPrice) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="seats" label="座位数" width="80">
          <template #default="scope">
            {{ scope.row.seats.length }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <el-button
              text
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:view" />
              </template>
              详情
            </el-button>
            <el-button
              text
              type="success"
              size="small"
              @click="handleViewSeats(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:tickets" />
              </template>
              座位图
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="selectedCinema && total > 0" class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 场次详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="场次详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentSchedule" class="space-y-4">
        <!-- 基本信息 -->
        <FaCard title="基本信息">
          <div class="grid grid-cols-2 gap-4">
            <div><strong>场次编码:</strong> {{ currentSchedule.code }}</div>
            <div><strong>影院编码:</strong> {{ currentSchedule.cinemaCode }}</div>
            <div><strong>影厅编码:</strong> {{ currentSchedule.screenCode }}</div>
            <div><strong>开始时间:</strong> {{ formatDateTime(currentSchedule.startTime) }}</div>
            <div><strong>连场标志:</strong> {{ formatPlaythroughFlag(currentSchedule.playthroughFlag) }}</div>
            <div><strong>活动名称:</strong> {{ currentSchedule.marketingName || '无' }}</div>
            <div><strong>提供商:</strong> {{ currentSchedule.provider }}</div>
            <div><strong>创建时间:</strong> {{ formatDateTime(currentSchedule.ctime) }}</div>
          </div>
        </FaCard>

        <!-- 影片信息 -->
        <FaCard title="影片信息">
          <div v-if="currentSchedule.films.length > 0" class="space-y-3">
            <div v-for="film in currentSchedule.films" :key="film.id" class="border rounded p-3">
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div><strong>影片名称:</strong> {{ film.name }}</div>
                <div><strong>影片编码:</strong> {{ film.code }}</div>
                <div><strong>语言:</strong> {{ film.lang }}</div>
                <div><strong>时长:</strong> {{ film.duration }}分钟</div>
                <div><strong>版本:</strong> {{ film.version }}</div>
                <div><strong>公映日期:</strong> {{ film.publishDate }}</div>
                <div><strong>发行商:</strong> {{ film.publisher }}</div>
                <div><strong>制片人:</strong> {{ film.producer }}</div>
                <div><strong>导演:</strong> {{ film.director }}</div>
                <div><strong>演员:</strong> {{ film.cast }}</div>
                <div class="col-span-2">
                  <strong>简介:</strong> {{ film.introduction }}
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-400">
            暂无影片信息
          </div>
        </FaCard>

        <!-- 价格信息 -->
        <FaCard title="价格信息">
          <div class="grid grid-cols-3 gap-4">
            <div><strong>最低票价:</strong> ¥{{ formatPrice(currentSchedule.price.lowestPrice) }}</div>
            <div><strong>标准票价:</strong> ¥{{ formatPrice(currentSchedule.price.standardPrice) }}</div>
            <div><strong>成人票价:</strong> ¥{{ formatPrice(currentSchedule.price.listingPrice) }}</div>
            <div><strong>增值服务费:</strong> ¥{{ formatPrice(currentSchedule.price.serviceAddFee) }}</div>
            <div><strong>影院补贴:</strong> ¥{{ formatPrice(currentSchedule.price.cinemaAllowance) }}</div>
            <div><strong>更新时间:</strong> {{ formatDateTime(currentSchedule.price.mtime) }}</div>
          </div>
        </FaCard>
      </div>
    </el-dialog>

    <!-- 座位图对话框 -->
    <el-dialog
      v-model="seatsDialogVisible"
      title="座位图"
      width="900px"
      :close-on-click-modal="false"
    >
      <div v-if="currentSeats.length > 0">
        <div class="mb-4 text-sm text-gray-600">
          总座位数: {{ currentSeats.length }}
        </div>
        <div class="max-h-96 overflow-auto">
          <el-table :data="currentSeats" border size="small">
            <el-table-column prop="seatCode" label="座位编码" width="100" />
            <el-table-column label="位置" width="100">
              <template #default="scope">
                {{ scope.row.rowNum }}排{{ scope.row.columnNum }}座
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag
                  :type="scope.row.status === 'Available' ? 'success'
                    : scope.row.status === 'Sold' ? 'danger'
                      : scope.row.status === 'Locked' ? 'warning' : 'info'"
                >
                  {{ formatSeatStatus(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="levelName" label="座位等级" width="100" />
            <el-table-column prop="memberLevelCode" label="会员限制" width="120" />
            <el-table-column label="坐标" width="100">
              <template #default="scope">
                ({{ scope.row.xCoord }}, {{ scope.row.yCoord }})
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div v-else class="py-8 text-center text-gray-400">
        暂无座位信息
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-3 > * + * {
  margin-top: 0.75rem;
}
</style>
