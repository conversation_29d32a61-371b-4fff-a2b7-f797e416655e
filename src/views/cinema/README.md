# Cinema 影院管理模块

本模块包含影院相关的管理功能，严格按照API文档实现。

## 模块结构

```
src/views/cinema/
├── README.md           # 模块说明文档
├── cinema.vue          # 影院列表管理（原有）
├── film.vue            # 影片管理
└── schedule.vue        # 场次管理
```

## 功能模块

### 1. 影片管理 (`film.vue`)

**功能描述**: 管理影院的影片信息，包括影片详情、演职人员等

**API接口**: 
- 权限: `/data/film/query`
- 接口: `POST /data/film/search`

**主要功能**:
- 影片列表查询（支持分页）
- 按影片名称、版本、提供商搜索
- 影片详情查看
- 演职人员信息展示

**数据结构**:
- 影片基本信息：编码、名称、语言、时长、版本等
- 演职人员信息：导演、主演等角色信息
- 发行信息：公映日期、提供商等

### 2. 场次管理 (`schedule.vue`)

**功能描述**: 管理影院的放映场次，包括排期、价格、座位等信息

**API接口**:
- 权限: `/ticket/play/query`
- 接口: `POST /ticket/play/search`

**主要功能**:
- 场次列表查询（支持分页）
- 按影院编码搜索
- 场次详情查看
- 座位图查看
- 价格信息展示

**数据结构**:
- 场次基本信息：编码、影院、影厅、开始时间等
- 影片信息：关联的影片详情
- 价格信息：最低价、标准价、成人价等
- 座位信息：座位状态、坐标、等级等

## API接口

所有接口都在 `src/api/modules/cinema.ts` 中定义，包含完整的类型定义。

### 影片管理接口
- `getFilmList()` - 获取影片列表
- `getFilmDetail()` - 获取影片详情

### 场次管理接口
- `getScheduleList()` - 获取场次列表
- `getScheduleDetail()` - 获取场次详情
- `getScheduleSeats()` - 获取场次座位信息

### 其他接口
- 影厅管理接口
- 票务相关接口
- 营销活动接口

## 类型定义

所有类型定义都在 `src/types/cinema.d.ts` 中，包括：

- `Film` - 影片信息
- `FilmMarker` - 演职人员信息
- `Schedule` - 场次信息
- `ScheduleFilm` - 场次中的影片信息
- `SchedulePrice` - 场次价格信息
- `ScheduleSeat` - 座位信息

## 路由配置

在 `src/router/modules/cinema.ts` 中配置：

```typescript
{
  path: 'film',
  name: 'CinemaFilm',
  component: () => import('@/views/cinema/film.vue'),
  meta: {
    title: '影片管理',
    icon: 'i-ri:film-line',
    auth: '/data/film/query',
  },
},
{
  path: 'schedule',
  name: 'CinemaSchedule',
  component: () => import('@/views/cinema/schedule.vue'),
  meta: {
    title: '场次管理',
    icon: 'i-ri:calendar-schedule-line',
    auth: '/ticket/play/query',
  },
}
```

## 使用说明

1. **开发模式**: 当前使用模拟数据，API调用失败时会自动fallback到模拟数据
2. **生产模式**: 取消API调用的注释，删除模拟数据相关代码
3. **权限控制**: 每个页面都配置了对应的权限码
4. **数据格式**: 严格按照API文档的响应格式实现

## 注意事项

1. 所有时间戳都使用毫秒级时间戳
2. 价格以分为单位存储，显示时转换为元
3. 座位状态有完整的枚举定义
4. 连场标志使用 'Yes'/'No' 字符串
5. 所有搜索参数都是可选的

## 后续扩展

可以根据需要添加以下功能：
- 影片海报上传
- 场次批量操作
- 座位图可视化编辑
- 价格策略管理
- 营销活动关联
