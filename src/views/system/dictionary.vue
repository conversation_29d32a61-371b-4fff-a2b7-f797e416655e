<route lang="yaml">
meta:
  title: 字典管理
  icon: i-ri:book-line
  # auth: ''
</route>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { nextTick, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
// TODO: 暂时注释掉，等API接口完成后再启用
// import apiSystem from '@/api/modules/system'

defineOptions({
  name: 'SystemDictionary',
})

// 字典类型数据接口
interface DictType {
  id: string
  dictType: string
  dictName: string
  description?: string
  status: number
  sort?: number
  ctime: number
}

// 字典项数据接口
interface DictItem {
  id: string
  dictType: string
  dictLabel: string
  dictValue: string
  description?: string
  status: number
  sort?: number
  ctime: number
}

// 响应式数据
const loading = ref(false)
const dictTypeList = ref<DictType[]>([])
const dictItemList = ref<DictItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 当前选中的字典类型
const selectedDictType = ref<DictType | null>(null)

// 搜索条件
const searchForm = ref({
  dictType: '',
  dictName: '',
  status: undefined as number | undefined,
})

// 字典项搜索条件
const itemSearchForm = ref({
  dictLabel: '',
  status: undefined as number | undefined,
})

// 对话框相关
const dialogVisible = ref(false)
const dialogLoading = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentTab = ref('type') // 'type' | 'item'

// 表单引用
const formRef = ref()

// 字典类型表单数据
const typeFormData = ref({
  id: '',
  dictType: '',
  dictName: '',
  description: '',
  status: 1,
  sort: 0,
})

// 字典项表单数据
const itemFormData = ref({
  id: '',
  dictType: '',
  dictLabel: '',
  dictValue: '',
  description: '',
  status: 1,
  sort: 0,
})

// 表单验证规则
const typeFormRules = {
  dictType: [
    { required: true, message: '请输入字典类型编码', trigger: 'blur' },
    { pattern: /^[a-z]\w*$/i, message: '字典类型编码只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' },
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
  ],
}

const itemFormRules = {
  dictLabel: [
    { required: true, message: '请输入字典标签', trigger: 'blur' },
  ],
  dictValue: [
    { required: true, message: '请输入字典值', trigger: 'blur' },
  ],
}

// 状态选项
const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 },
]

// 获取字典类型列表
async function getDictTypeList() {
  loading.value = true
  try {
    // TODO: 暂时注释掉API调用，使用模拟数据
    // const res: any = await apiSystem.getDictTypeList({
    //   page: currentPage.value - 1,
    //   size: pageSize.value,
    //   dictType: searchForm.value.dictType || undefined,
    //   dictName: searchForm.value.dictName || undefined,
    //   status: searchForm.value.status,
    // })
    // if (res.code === 0) {
    //   dictTypeList.value = res.data.content || []
    //   total.value = res.data.total || 0
    // }
    // else {
    //   toast.error(res.msg || '获取字典类型列表失败')
    // }

    // 模拟数据
    const mockData = [
      {
        id: '1',
        dictType: 'user_status',
        dictName: '用户状态',
        description: '用户账号状态字典',
        status: 1,
        sort: 1,
        ctime: Date.now() - 86400000,
      },
      {
        id: '2',
        dictType: 'user_type',
        dictName: '用户类型',
        description: '用户类型分类字典',
        status: 1,
        sort: 2,
        ctime: Date.now() - 172800000,
      },
      {
        id: '3',
        dictType: 'priority_level',
        dictName: '优先级',
        description: '任务优先级字典',
        status: 1,
        sort: 3,
        ctime: Date.now() - 259200000,
      },
      {
        id: '4',
        dictType: 'order_status',
        dictName: '订单状态',
        description: '订单状态字典',
        status: 0,
        sort: 4,
        ctime: Date.now() - 345600000,
      },
      {
        id: '5',
        dictType: 'payment_method',
        dictName: '支付方式',
        description: '支付方式字典',
        status: 1,
        sort: 5,
        ctime: Date.now() - *********,
      },
      // 影厅类型
      {
        id: '6',
        dictType: 'screen_type',
        dictName: '影厅类型',
        description: '影厅类型字典',
        status: 1,
        sort: 6,
        ctime: Date.now() - *********,
      },
    ]

    // 模拟搜索过滤
    let filteredData = mockData
    if (searchForm.value.dictType) {
      filteredData = filteredData.filter(item =>
        item.dictType.toLowerCase().includes(searchForm.value.dictType.toLowerCase()),
      )
    }
    if (searchForm.value.dictName) {
      filteredData = filteredData.filter(item =>
        item.dictName.includes(searchForm.value.dictName),
      )
    }
    if (searchForm.value.status !== undefined) {
      filteredData = filteredData.filter(item => item.status === searchForm.value.status)
    }

    dictTypeList.value = filteredData
    total.value = filteredData.length
  }
  catch (error: any) {
    toast.error(error.message || '获取字典类型列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取字典项列表
async function getDictItemList() {
  if (!selectedDictType.value) {
    return
  }

  loading.value = true
  try {
    // TODO: 暂时注释掉API调用，使用模拟数据
    // const res: any = await apiSystem.getDictItemList({
    //   page: currentPage.value - 1,
    //   size: pageSize.value,
    //   dictType: selectedDictType.value.dictType,
    //   dictLabel: itemSearchForm.value.dictLabel || undefined,
    //   status: itemSearchForm.value.status,
    // })
    // if (res.code === 0) {
    //   dictItemList.value = res.data.content || []
    //   total.value = res.data.total || 0
    // }
    // else {
    //   toast.error(res.msg || '获取字典项列表失败')
    // }

    // 模拟字典项数据
    const mockItemsMap: Record<string, DictItem[]> = {
      user_status: [
        {
          id: '1',
          dictType: 'user_status',
          dictLabel: '正常',
          dictValue: '1',
          description: '用户状态正常',
          status: 1,
          sort: 1,
          ctime: Date.now() - 86400000,
        },
        {
          id: '2',
          dictType: 'user_status',
          dictLabel: '禁用',
          dictValue: '0',
          description: '用户被禁用',
          status: 1,
          sort: 2,
          ctime: Date.now() - 86400000,
        },
        {
          id: '3',
          dictType: 'user_status',
          dictLabel: '锁定',
          dictValue: '2',
          description: '用户被锁定',
          status: 1,
          sort: 3,
          ctime: Date.now() - 86400000,
        },
      ],
      user_type: [
        {
          id: '4',
          dictType: 'user_type',
          dictLabel: '管理员',
          dictValue: 'admin',
          description: '系统管理员',
          status: 1,
          sort: 1,
          ctime: Date.now() - 172800000,
        },
        {
          id: '5',
          dictType: 'user_type',
          dictLabel: '普通用户',
          dictValue: 'user',
          description: '普通用户',
          status: 1,
          sort: 2,
          ctime: Date.now() - 172800000,
        },
        {
          id: '6',
          dictType: 'user_type',
          dictLabel: '访客',
          dictValue: 'guest',
          description: '访客用户',
          status: 0,
          sort: 3,
          ctime: Date.now() - 172800000,
        },
      ],
      priority_level: [
        {
          id: '7',
          dictType: 'priority_level',
          dictLabel: '高',
          dictValue: 'high',
          description: '高优先级',
          status: 1,
          sort: 1,
          ctime: Date.now() - 259200000,
        },
        {
          id: '8',
          dictType: 'priority_level',
          dictLabel: '中',
          dictValue: 'medium',
          description: '中等优先级',
          status: 1,
          sort: 2,
          ctime: Date.now() - 259200000,
        },
        {
          id: '9',
          dictType: 'priority_level',
          dictLabel: '低',
          dictValue: 'low',
          description: '低优先级',
          status: 1,
          sort: 3,
          ctime: Date.now() - 259200000,
        },
      ],
      order_status: [
        {
          id: '10',
          dictType: 'order_status',
          dictLabel: '待支付',
          dictValue: 'pending',
          description: '订单待支付',
          status: 1,
          sort: 1,
          ctime: Date.now() - 345600000,
        },
        {
          id: '11',
          dictType: 'order_status',
          dictLabel: '已支付',
          dictValue: 'paid',
          description: '订单已支付',
          status: 1,
          sort: 2,
          ctime: Date.now() - 345600000,
        },
        {
          id: '12',
          dictType: 'order_status',
          dictLabel: '已取消',
          dictValue: 'cancelled',
          description: '订单已取消',
          status: 1,
          sort: 3,
          ctime: Date.now() - 345600000,
        },
      ],
      payment_method: [
        {
          id: '13',
          dictType: 'payment_method',
          dictLabel: '微信支付',
          dictValue: 'wechat',
          description: '微信支付',
          status: 1,
          sort: 1,
          ctime: Date.now() - *********,
        },
        {
          id: '14',
          dictType: 'payment_method',
          dictLabel: '支付宝',
          dictValue: 'alipay',
          description: '支付宝支付',
          status: 1,
          sort: 2,
          ctime: Date.now() - *********,
        },
        {
          id: '15',
          dictType: 'payment_method',
          dictLabel: '银行卡',
          dictValue: 'bank',
          description: '银行卡支付',
          status: 1,
          sort: 3,
          ctime: Date.now() - *********,
        },
      ],
      screen_type: [
        {
          id: '16',
          dictType: 'screen_type',
          dictLabel: '2D',
          dictValue: '2d',
          description: '2D影厅',
          status: 1,
          sort: 1,
          ctime: Date.now() - *********,
        },
        {
          id: '17',
          dictType: 'screen_type',
          dictLabel: '3D',
          dictValue: '3d',
          description: '3D影厅',
          status: 1,
          sort: 2,
          ctime: Date.now() - *********,
        },
      ],
    }

    let mockItems = mockItemsMap[selectedDictType.value.dictType] || []

    // 模拟搜索过滤
    if (itemSearchForm.value.dictLabel) {
      mockItems = mockItems.filter(item =>
        item.dictLabel.includes(itemSearchForm.value.dictLabel),
      )
    }
    if (itemSearchForm.value.status !== undefined) {
      mockItems = mockItems.filter(item => item.status === itemSearchForm.value.status)
    }

    dictItemList.value = mockItems
    total.value = mockItems.length
  }
  catch (error: any) {
    toast.error(error.message || '获取字典项列表失败')
  }
  finally {
    loading.value = false
  }
}

// 搜索
function handleSearch() {
  currentPage.value = 1
  if (currentTab.value === 'type') {
    getDictTypeList()
  }
  else {
    getDictItemList()
  }
}

// 重置搜索
function handleReset() {
  if (currentTab.value === 'type') {
    searchForm.value = {
      dictType: '',
      dictName: '',
      status: undefined,
    }
  }
  else {
    itemSearchForm.value = {
      dictLabel: '',
      status: undefined,
    }
  }
  handleSearch()
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  if (currentTab.value === 'type') {
    getDictTypeList()
  }
  else {
    getDictItemList()
  }
}

// 分页大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  if (currentTab.value === 'type') {
    getDictTypeList()
  }
  else {
    getDictItemList()
  }
}

// 选择字典类型
function selectDictType(dictType: DictType) {
  selectedDictType.value = dictType
  currentTab.value = 'item'
  currentPage.value = 1
  getDictItemList()
}

// 返回字典类型列表
function backToTypeList() {
  selectedDictType.value = null
  currentTab.value = 'type'
  currentPage.value = 1
  getDictTypeList()
}

// 打开新增对话框
function openAddDialog() {
  isEdit.value = false
  if (currentTab.value === 'type') {
    dialogTitle.value = '新增字典类型'
    typeFormData.value = {
      id: '',
      dictType: '',
      dictName: '',
      description: '',
      status: 1,
      sort: 0,
    }
  }
  else {
    dialogTitle.value = '新增字典项'
    itemFormData.value = {
      id: '',
      dictType: selectedDictType.value?.dictType || '',
      dictLabel: '',
      dictValue: '',
      description: '',
      status: 1,
      sort: 0,
    }
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 打开编辑对话框
function openEditDialog(row: DictType | DictItem) {
  isEdit.value = true
  if (currentTab.value === 'type') {
    dialogTitle.value = '编辑字典类型'
    const typeRow = row as DictType
    typeFormData.value = {
      id: typeRow.id,
      dictType: typeRow.dictType,
      dictName: typeRow.dictName,
      description: typeRow.description || '',
      status: typeRow.status,
      sort: typeRow.sort || 0,
    }
  }
  else {
    dialogTitle.value = '编辑字典项'
    const itemRow = row as DictItem
    itemFormData.value = {
      id: itemRow.id,
      dictType: itemRow.dictType,
      dictLabel: itemRow.dictLabel,
      dictValue: itemRow.dictValue,
      description: itemRow.description || '',
      status: itemRow.status,
      sort: itemRow.sort || 0,
    }
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 保存
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    await formRef.value.validate()
    dialogLoading.value = true

    // TODO: 暂时注释掉API调用，使用模拟操作
    // let res: any
    // if (currentTab.value === 'type') {
    //   if (isEdit.value) {
    //     res = await apiSystem.editDictType(typeFormData.value)
    //   }
    //   else {
    //     const { id, ...addData } = typeFormData.value
    //     res = await apiSystem.addDictType(addData)
    //   }
    // }
    // else {
    //   if (isEdit.value) {
    //     res = await apiSystem.editDictItem(itemFormData.value)
    //   }
    //   else {
    //     const { id, ...addData } = itemFormData.value
    //     res = await apiSystem.addDictItem(addData)
    //   }
    // }

    // if (res.code === 0) {
    //   toast.success(isEdit.value ? '编辑成功' : '新增成功')
    //   dialogVisible.value = false
    //   if (currentTab.value === 'type') {
    //     getDictTypeList()
    //   }
    //   else {
    //     getDictItemList()
    //   }
    // }
    // else {
    //   toast.error(res.msg || (isEdit.value ? '编辑失败' : '新增失败'))
    // }

    // 模拟保存成功
    setTimeout(() => {
      toast.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      if (currentTab.value === 'type') {
        getDictTypeList()
      }
      else {
        getDictItemList()
      }
    }, 500)
  }
  catch (error: any) {
    if (error !== 'cancel') {
      toast.error(error.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  finally {
    setTimeout(() => {
      dialogLoading.value = false
    }, 500)
  }
}

// 删除
async function handleDelete(row: DictType | DictItem) {
  try {
    const name = currentTab.value === 'type' ? (row as DictType).dictName : (row as DictItem).dictLabel
    await ElMessageBox.confirm(
      `确定要删除"${name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    // TODO: 暂时注释掉API调用，使用模拟操作
    // let res: any
    // if (currentTab.value === 'type') {
    //   res = await apiSystem.deleteDictType({ id: row.id })
    // }
    // else {
    //   res = await apiSystem.deleteDictItem({ id: row.id })
    // }

    // if (res.code === 0) {
    //   toast.success('删除成功')
    //   if (currentTab.value === 'type') {
    //     getDictTypeList()
    //   }
    //   else {
    //     getDictItemList()
    //   }
    // }
    // else {
    //   toast.error(res.msg || '删除失败')
    // }

    // 模拟删除成功
    toast.success('删除成功')
    if (currentTab.value === 'type') {
      getDictTypeList()
    }
    else {
      getDictItemList()
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      toast.error(error.message || '删除失败')
    }
  }
}

// 格式化时间
function formatDate(timestamp: number) {
  return new Date(timestamp).toLocaleString()
}

// 格式化状态
function formatStatus(status: number) {
  return status === 1 ? '启用' : '禁用'
}

// 初始化
onMounted(() => {
  getDictTypeList()
})
</script>

<template>
  <div>
    <FaPageHeader
      :title="selectedDictType ? `字典项管理 - ${selectedDictType.dictName}` : '字典管理'"
      :description="selectedDictType ? '管理字典项的标签和值' : '管理系统字典类型和字典项'"
    >
      <template v-if="selectedDictType">
        <el-button @click="backToTypeList">
          <template #icon>
            <FaIcon name="i-ep:back" />
          </template>
          返回字典类型
        </el-button>
      </template>
    </FaPageHeader>

    <FaPageMain>
      <!-- 搜索区域 -->
      <div class="mb-4 flex flex-wrap items-center gap-4">
        <template v-if="currentTab === 'type'">
          <el-input
            v-model="searchForm.dictType"
            placeholder="字典类型编码"
            clearable
            style="width: 200px;"
            @keyup.enter="handleSearch"
          />
          <el-input
            v-model="searchForm.dictName"
            placeholder="字典名称"
            clearable
            style="width: 200px;"
            @keyup.enter="handleSearch"
          />
          <el-select
            v-model="searchForm.status"
            placeholder="状态"
            clearable
            style="width: 120px;"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
        <template v-else>
          <el-input
            v-model="itemSearchForm.dictLabel"
            placeholder="字典标签"
            clearable
            style="width: 200px;"
            @keyup.enter="handleSearch"
          />
          <el-select
            v-model="itemSearchForm.status"
            placeholder="状态"
            clearable
            style="width: 120px;"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>

        <el-button type="primary" @click="handleSearch">
          <template #icon>
            <FaIcon name="i-ep:search" />
          </template>
          搜索
        </el-button>
        <el-button @click="handleReset">
          <template #icon>
            <FaIcon name="i-ep:refresh" />
          </template>
          重置
        </el-button>
        <el-button type="primary" @click="openAddDialog">
          <template #icon>
            <FaIcon name="i-ep:plus" />
          </template>
          {{ currentTab === 'type' ? '新增字典类型' : '新增字典项' }}
        </el-button>
      </div>

      <!-- 字典类型表格 -->
      <el-table
        v-if="currentTab === 'type'"
        v-loading="loading"
        :data="dictTypeList"

        stripe border
      >
        <el-table-column prop="dictType" label="字典类型编码" min-width="150" />
        <el-table-column prop="dictName" label="字典名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template #default="scope">
            <el-button
              text
              type="primary"
              size="small"
              @click="selectDictType(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:view" />
              </template>
              字典项
            </el-button>
            <el-button
              text
              type="primary"
              size="small"
              @click="openEditDialog(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:edit" />
              </template>
              编辑
            </el-button>
            <el-button
              text
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:delete" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 字典项表格 -->
      <el-table
        v-else
        v-loading="loading"
        :data="dictItemList"

        stripe border
      >
        <el-table-column prop="dictLabel" label="字典标签" min-width="150" />
        <el-table-column prop="dictValue" label="字典值" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ formatStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center" />
        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" align="center">
          <template #default="scope">
            <el-button
              text
              type="primary"
              size="small"
              @click="openEditDialog(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:edit" />
              </template>
              编辑
            </el-button>
            <el-button
              text
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:delete" />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
    >
      <!-- 字典类型表单 -->
      <el-form
        v-if="currentTab === 'type'"
        ref="formRef"
        :model="typeFormData"
        :rules="typeFormRules"
        label-width="120px"
      >
        <el-form-item label="字典类型编码" prop="dictType">
          <el-input
            v-model="typeFormData.dictType"
            placeholder="请输入字典类型编码"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="字典名称" prop="dictName">
          <el-input
            v-model="typeFormData.dictName"
            placeholder="请输入字典名称"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="typeFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="typeFormData.status">
            <el-radio :value="1">
              启用
            </el-radio>
            <el-radio :value="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number
            v-model="typeFormData.sort"
            :min="0"
            :max="9999"
            controls-position="right"
          />
        </el-form-item>
      </el-form>

      <!-- 字典项表单 -->
      <el-form
        v-else
        ref="formRef"
        :model="itemFormData"
        :rules="itemFormRules"
        label-width="120px"
      >
        <el-form-item label="字典类型">
          <el-input
            v-model="itemFormData.dictType"
            disabled
          />
        </el-form-item>
        <el-form-item label="字典标签" prop="dictLabel">
          <el-input
            v-model="itemFormData.dictLabel"
            placeholder="请输入字典标签"
          />
        </el-form-item>
        <el-form-item label="字典值" prop="dictValue">
          <el-input
            v-model="itemFormData.dictValue"
            placeholder="请输入字典值"
          />
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="itemFormData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="itemFormData.status">
            <el-radio :value="1">
              启用
            </el-radio>
            <el-radio :value="0">
              禁用
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number
            v-model="itemFormData.sort"
            :min="0"
            :max="9999"
            controls-position="right"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">
            取消
          </el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="handleSave"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
