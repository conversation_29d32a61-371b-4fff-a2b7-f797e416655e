<route lang="yaml">
meta:
  title: 权限菜单
  icon: i-ri:menu-line
  # auth: ''
</route>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { nextTick, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import apiSystem from '@/api/modules/system'
import dayjs from '@/utils/dayjs'

defineOptions({
  name: 'SystemMenu',
})

interface MenuItem {
  id: string
  fullPath: string
  path: string
  name: string
  parentId: string
  ctime: number
  children?: MenuItem[]
  hasChildren?: boolean
}

// 开启删除功能
const isDelete = ref(true)
const loading = ref(false)
const menuList = ref<MenuItem[]>([])
const refreshTable = ref(true)
const tableRef = ref()

// 编辑相关
const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = ref({
  id: '',
  name: '',
  path: '',
  fullPath: '',
})

const editRules = {
  name: [
    { required: true, message: '请输入菜单名称', trigger: 'blur' },
  ],
  path: [
    { required: true, message: '请输入路径', trigger: 'blur' },
  ],
  fullPath: [
    { required: true, message: '请输入完整路径', trigger: 'blur' },
  ],
}

// 格式化日期
function formatDate(timestamp: number) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')
}

// 获取菜单列表
async function getMenuList() {
  loading.value = true
  try {
    const res: any = await apiSystem.getMenuListAll()
    if (res.code === 0) {
      const menus = res.data.content || []
      // 构建树形结构
      menuList.value = buildMenuTree(menus)
    }
    else {
      toast.error(res.msg || '获取菜单列表失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取菜单列表失败')
  }
  finally {
    loading.value = false
  }
}

// 构建菜单树结构
function buildMenuTree(menuList: MenuItem[]): MenuItem[] {
  const menuMap = new Map<string, MenuItem>()
  const rootMenus: MenuItem[] = []

  // 第一步：创建菜单映射，初始化 children 数组
  menuList.forEach((menu) => {
    if (menu.path !== 'path') {
      return
    }
    menuMap.set(menu.id, {
      ...menu,
      children: [],
    })
  })

  // 第二步：构建树形结构
  menuList.forEach((menu) => {
    const menuNode = menuMap.get(menu.id)!

    // 检查是否有父节点
    if (menu.parentId && menu.parentId.trim() !== '' && menuMap.has(menu.parentId)) {
      // 有父节点，添加到父节点的 children 中
      const parentNode = menuMap.get(menu.parentId)!
      parentNode.children!.push(menuNode)
    }
    else {
      // 没有父节点或父节点不存在，说明是根目录
      rootMenus.push(menuNode)
    }
  })

  // 第三步：按创建时间排序
  const sortMenus = (menus: MenuItem[]) => {
    menus.sort((a, b) => a.ctime - b.ctime)
    menus.forEach((menu) => {
      if (menu.children && menu.children.length > 0) {
        sortMenus(menu.children)
      }
    })
  }

  sortMenus(rootMenus)

  return rootMenus
}

// 编辑菜单
function handleUpdate(row: MenuItem) {
  editForm.value = {
    id: row.id,
    name: row.name,
    path: row.path,
    fullPath: row.fullPath,
  }
  editDialogVisible.value = true
}

// 提交编辑
async function handleEditSubmit() {
  if (!editFormRef.value) {
    return
  }

  try {
    await editFormRef.value.validate()

    const res: any = await apiSystem.editMenu({
      id: editForm.value.id,
      name: editForm.value.name,
    })
    if (res.code === 0) {
      toast.success('编辑成功')
      editDialogVisible.value = false
      // 刷新菜单列表
      refreshTableData()
    }
    else {
      toast.error(res.msg || '编辑失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '编辑失败')
  }
}

// 删除菜单
async function handleDelete(row: MenuItem) {
  try {
    await ElMessageBox.confirm('确定要删除这个菜单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })

    const res: any = await apiSystem.deleteMenu({ id: row.id })
    if (res.code === 0) {
      toast.success('删除成功')
      refreshTableData()
    }
    else {
      toast.error(res.msg || '删除失败')
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      toast.error(error.message || '删除失败')
    }
  }
}

// 刷新表格数据
function refreshTableData() {
  refreshTable.value = false
  nextTick(() => {
    refreshTable.value = true
    getMenuList()
  })
}

onMounted(() => {
  getMenuList()
})
</script>

<template>
  <div>
    <FaPageHeader title="菜单管理" description="用于管理菜单和按钮等权限" />
    <FaPageMain>
      <el-table
        v-if="refreshTable"
        ref="tableRef"
        v-loading="loading"
        :data="menuList"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"

        default-expand-all border
      >
        <el-table-column prop="name" label="菜单名称" :show-overflow-tooltip="true" min-width="160">
          <template #default="scope">
            <span class="font-medium">{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="path" label="路径" :show-overflow-tooltip="true" min-width="200" /> -->
        <el-table-column prop="fullPath" label="完整路径" :show-overflow-tooltip="true" min-width="200" />
        <el-table-column prop="ctime" label="创建时间" min-width="180">
          <template #default="scope">
            {{ formatDate(scope.row.ctime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="170">
          <template #default="scope">
            <ElButton
              text
              type="primary"
              size="small"
              @click="handleUpdate(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:edit" />
              </template>
              编辑
            </ElButton>
            <ElButton
              v-if="isDelete"
              text
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              <template #icon>
                <FaIcon name="i-ep:delete" />
              </template>
              删除
            </ElButton>
          </template>
        </el-table-column>
      </el-table>
    </FaPageMain>

    <!-- 编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑菜单"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="editForm.name" placeholder="请输入菜单名称" />
        </el-form-item>
        <el-form-item label="路径" prop="path">
          <el-input v-model="editForm.path" placeholder="请输入路径" :disabled="true" />
        </el-form-item>
        <el-form-item label="完整路径" prop="fullPath">
          <el-input v-model="editForm.fullPath" placeholder="请输入完整路径" :disabled="true" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">
          取消
        </el-button>
        <el-button type="primary" @click="handleEditSubmit">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}
</style>
