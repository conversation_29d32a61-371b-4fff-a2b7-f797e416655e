<route lang="yaml">
meta:
  title: 角色管理
  icon: i-ri:user-settings-line
  # auth: ''
</route>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { nextTick, onMounted, ref } from 'vue'
import { toast } from 'vue-sonner'
import apiSystem from '@/api/modules/system'
import { useUserStore } from '@/store/modules/user'

defineOptions({
  name: 'SystemRoles',
})

// 角色数据接口
interface Role {
  id: string
  name: string
  menuIds?: string[]
  ctime: number
}

// 菜单数据接口
interface Menu {
  id: string
  fullPath: string
  path: string
  name: string
  parentId: string
  ctime: number
  children?: Menu[]
}

// 扁平化菜单项接口
interface FlatMenuItem {
  id: string
  name: string
  fullPath: string
  level: number
  parentName?: string
}

// 响应式数据
const loading = ref(false)
const roleList = ref<Role[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 对话框相关
const dialogVisible = ref(false)
const dialogTitle = ref('新增角色')
const dialogLoading = ref(false)
const isEdit = ref(false)

// 表单数据
const formData = ref({
  id: '',
  name: '',
  menuIds: [] as string[],
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 50, message: '角色名称长度在 2 到 50 个字符', trigger: 'blur' },
  ],
}

// 表单引用
const formRef = ref()
const nameInputRef = ref()
const treeRef = ref()

// 菜单树数据
const menuTreeData = ref<Menu[]>([])
const menuTreeLoading = ref(false)

// 扁平化菜单列表
const flatMenuList = ref<FlatMenuItem[]>([])

// 监听对话框关闭，清理状态
function handleDialogClose() {
  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    menuIds: [],
  }

  // 重置编辑状态
  isEdit.value = false
  dialogTitle.value = '新增角色'
}

// 将树形菜单转换为扁平化列表
function flattenMenus(menus: Menu[], level: number = 0, parentName?: string): FlatMenuItem[] {
  const result: FlatMenuItem[] = []

  menus.forEach((menu) => {
    result.push({
      id: menu.id,
      name: menu.name,
      fullPath: menu.fullPath,
      level,
      parentName,
    })

    if (menu.children && menu.children.length > 0) {
      result.push(...flattenMenus(menu.children, level + 1, menu.name))
    }
  })

  return result
}

// 获取角色列表
async function getRoleList() {
  loading.value = true
  try {
    const res: any = await apiSystem.getRoleList({
      page: currentPage.value - 1,
      size: pageSize.value,
    })
    if (res.code === 0) {
      roleList.value = res.data.content || []
      total.value = res.data.total || 0
    }
    else {
      toast.error(res.msg || '获取角色列表失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取角色列表失败')
  }
  finally {
    loading.value = false
  }
}

// 获取所有菜单数据（用于权限分配）
async function getMenuTree() {
  menuTreeLoading.value = true
  try {
    const res: any = await apiSystem.getMenuListAll()
    if (res.code === 0) {
      // 将扁平数据转换为树形结构
      menuTreeData.value = buildMenuTree(res.data.content || [])
      flatMenuList.value = flattenMenus(menuTreeData.value)
    }
    else {
      toast.error(res.msg || '获取菜单数据失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取菜单数据失败')
  }
  finally {
    menuTreeLoading.value = false
  }
}

// 构建菜单树结构
function buildMenuTree(menuList: Menu[]): Menu[] {
  const menuMap = new Map<string, Menu>()
  const rootMenus: Menu[] = []

  // 创建菜单映射
  menuList.forEach((menu) => {
    menuMap.set(menu.id, { ...menu, children: [] })
  })

  // 构建树形结构
  menuList.forEach((menu) => {
    const menuNode = menuMap.get(menu.id)!
    if (menu.parentId && menuMap.has(menu.parentId)) {
      const parentNode = menuMap.get(menu.parentId)!
      parentNode.children!.push(menuNode)
    }
    else {
      rootMenus.push(menuNode)
    }
  })

  return rootMenus
}

// 打开新增对话框
function openAddDialog() {
  isEdit.value = false
  dialogTitle.value = '新增角色'

  // 重置表单数据
  formData.value = {
    id: '',
    name: '',
    menuIds: [],
  }

  // 打开对话框
  dialogVisible.value = true

  // 等待DOM更新后聚焦输入框
  nextTick(() => {
    // 聚焦到角色名称输入框
    if (nameInputRef.value) {
      nameInputRef.value.focus()
    }
  })
}

// 打开编辑对话框
async function openEditDialog(row: Role) {
  isEdit.value = true
  dialogTitle.value = '编辑角色'

  try {
    // 获取角色详情
    const res: any = await apiSystem.getRoleDetail({ id: row.id })
    if (res.code === 0) {
      const roleDetail = res.data
      formData.value = {
        id: roleDetail.id,
        name: roleDetail.name,
        menuIds: roleDetail.menus?.map((menu: Menu) => menu.id) || [],
      }

      // 打开对话框
      dialogVisible.value = true

      // 等待DOM更新后设置Tree的选中状态
      nextTick(() => {
        if (treeRef.value && formData.value.menuIds.length > 0) {
          treeRef.value.setCheckedKeys(formData.value.menuIds)
        }
      })
    }
    else {
      toast.error(res.msg || '获取角色详情失败')
    }
  }
  catch (error: any) {
    toast.error(error.message || '获取角色详情失败')
  }
}

// 保存角色
async function handleSave() {
  if (!formRef.value) {
    return
  }

  try {
    // 表单验证
    await formRef.value.validate()

    dialogLoading.value = true
    const res: any = await apiSystem.saveRole(formData.value)
    if (res.code === 0) {
      toast.success(isEdit.value ? '编辑成功' : '新增成功')
      dialogVisible.value = false
      getRoleList()
    }
    else {
      toast.error(res.msg || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  catch (error: any) {
    if (error !== false) { // 表单验证失败不显示错误
      toast.error(error.message || (isEdit.value ? '编辑失败' : '新增失败'))
    }
  }
  finally {
    dialogLoading.value = false
  }
}

// 删除角色
async function handleDelete(row: Role) {
  try {
    await ElMessageBox.confirm(
      `确定要删除角色"${row.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      },
    )

    const res: any = await apiSystem.deleteRole({ id: row.id })
    if (res.code === 0) {
      toast.success('删除成功')
      getRoleList()
    }
    else {
      toast.error(res.msg || '删除失败')
    }
  }
  catch (error: any) {
    if (error !== 'cancel') {
      toast.error(error.message || '删除失败')
    }
  }
}

// 处理菜单选择
function handleMenuCheck() {
  // Tree组件的check事件会自动处理父子联动
  // 我们只需要获取当前选中的节点即可
  if (treeRef.value) {
    const checkedKeys = treeRef.value.getCheckedKeys()
    const halfCheckedKeys = treeRef.value.getHalfCheckedKeys()
    // 合并完全选中和半选中的节点
    formData.value.menuIds = [...checkedKeys, ...halfCheckedKeys]
  }
}

// 获取所有菜单的ID（用于全选功能）
function getAllMenuKeys(menus: Menu[]): string[] {
  const keys: string[] = []
  const traverse = (items: Menu[]) => {
    items.forEach((item) => {
      keys.push(item.id)
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(menus)
  return keys
}

// 获取所有菜单的数量
function getAllMenuCount(): number {
  return getAllMenuKeys(menuTreeData.value).length
}

// 全选/取消全选菜单
function toggleSelectAll() {
  if (!treeRef.value) {
    return
  }

  const allKeys = getAllMenuKeys(menuTreeData.value)
  const currentKeys = formData.value.menuIds

  if (currentKeys.length === allKeys.length) {
    // 当前全选，则取消全选
    treeRef.value.setCheckedKeys([])
    formData.value.menuIds = []
  }
  else {
    // 当前未全选，则全选
    treeRef.value.setCheckedKeys(allKeys)
    formData.value.menuIds = allKeys
  }
}

// 获取选中状态描述
function getSelectionDescription() {
  const checkedCount = formData.value.menuIds.length

  if (checkedCount === 0) {
    return '未选择任何菜单'
  }
  else {
    return `已选择 ${checkedCount} 个菜单`
  }
}

// 获取选中的菜单名称列表
function getSelectedMenuNames() {
  const selectedNames: string[] = []

  const traverse = (items: Menu[]) => {
    items.forEach((item) => {
      if (formData.value.menuIds.includes(item.id)) {
        selectedNames.push(item.name)
      }
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }

  traverse(menuTreeData.value)
  return selectedNames
}

// 分页变化
function handlePageChange(page: number) {
  currentPage.value = page
  getRoleList()
}

// 页面大小变化
function handleSizeChange(size: number) {
  pageSize.value = size
  currentPage.value = 1
  getRoleList()
}

// 初始化
onMounted(() => {
  getRoleList()
  getMenuTree()
})

// 用户权限检查
const userStore = useUserStore()
</script>

<template>
  <div>
    <FaPageHeader title="角色管理" description="管理系统角色和权限分配" />

    <FaPageMain>
      <!-- 操作按钮 -->
      <div class="mb-4 flex items-center justify-between">
        <div class="flex items-center gap-2">
          <FaButton
            v-if="userStore.permissions.includes('/system/role/save')"
            type="primary"
            icon="i-ri:add-line"
            @click="openAddDialog"
          >
            新增角色
          </FaButton>
          <FaButton
            type="success"
            icon="i-ri:refresh-line"
            @click="getRoleList"
          >
            刷新
          </FaButton>
        </div>
      </div>

      <!-- 角色列表 -->
      <el-table
        v-loading="loading"
        :data="roleList"
        style="width: 100%;"
        border
      >
        <el-table-column prop="name" label="角色名称" min-width="150">
          <template #default="scope">
            <span class="font-medium">{{ scope.row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="menuIds" label="权限菜单" min-width="200">
          <template #default="scope">
            <el-tag
              v-if="scope.row.menuIds && scope.row.menuIds.length > 0"
              type="success"
              size="small"
            >
              {{ scope.row.menuIds.length }} 个菜单
            </el-tag>
            <el-tag v-else type="info" size="small">
              无权限
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="ctime" label="创建时间" width="180">
          <template #default="scope">
            <span class="text-gray-500">
              {{ new Date(scope.row.ctime).toLocaleString() }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="170" align="center">
          <template #default="scope">
            <FaAuth :value="['/system/role/save']">
              <el-button
                text
                type="primary"
                size="small"
                @click="openEditDialog(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:edit" />
                </template>
                编辑
              </el-button>
            </FaAuth>
            <FaAuth :value="['/system/role/delete']">
              <el-button
                text
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                <template #icon>
                  <FaIcon name="i-ep:delete" />
                </template>
                删除
              </el-button>
            </FaAuth>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-end">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </FaPageMain>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="60%"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form ref="formRef" :model="formData" label-width="100px" :rules="formRules">
        <el-form-item label="角色名称" prop="name">
          <el-input
            ref="nameInputRef"
            v-model="formData.name"
            placeholder="请输入角色名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="权限菜单">
          <div class="flex flex-col gap-2" style="width: 100%;margin-bottom: 10px;">
            <div class="flex items-center gap-2">
              <el-button
                type="primary"
                size="small"
                @click="toggleSelectAll"
              >
                {{ formData.menuIds.length === getAllMenuCount() ? '取消全选' : '全选' }}
              </el-button>
              <span class="text-sm text-gray-500">
                {{ getSelectionDescription() }}
              </span>
            </div>

            <!-- 选中菜单详情 -->
            <div v-if="formData.menuIds.length > 0" class="rounded bg-gray-50 p-2">
              <div class="mb-1 text-sm text-gray-600">
                已选菜单：
              </div>
              <div class="flex flex-wrap gap-1">
                <el-tag
                  v-for="name in getSelectedMenuNames()"
                  :key="name"
                  size="small"
                  type="success"
                >
                  {{ name }}
                </el-tag>
              </div>
            </div>
          </div>

          <div v-loading="menuTreeLoading" class="menu-checkbox-container">
            <el-tree
              ref="treeRef"
              :data="menuTreeData"

              node-key="id"
              :props="{
                children: 'children',
                label: 'name',
              }"
              :check-strictly="false"
              :expand-on-click-node="false"
              default-expand-all show-checkbox
              @check="handleMenuCheck"
            />
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="dialogLoading"
            @click="handleSave"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.menu-checkbox-container {
  width: 100%;
  max-height: 50vh;
  padding: 12px;
  overflow-y: auto;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

:deep(.el-tree) {
  background: transparent;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-tree-node__label) {
  font-size: 14px;
}

:deep(.el-checkbox) {
  margin-right: 8px;
}
</style>
