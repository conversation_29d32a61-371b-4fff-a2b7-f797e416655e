// UnoCSS 配置文件
// UnoCSS 是一个即时原子化 CSS 引擎，提供高性能的样式生成

import type { Theme } from 'unocss/preset-uno'
import { entriesToCss } from '@unocss/core'
import presetLegacyCompat from '@unocss/preset-legacy-compat'
import {
  defineConfig,
  presetAttributify, // 属性化预设，支持属性模式写法
  presetIcons, // 图标预设，支持各种图标库
  presetTypography, // 排版预设，提供排版相关的工具类
  presetWind3, // Tailwind CSS v3 兼容预设
  transformerCompileClass, // 编译类转换器
  transformerDirectives, // 指令转换器，支持 @apply 等指令
  transformerVariantGroup, // 变体组转换器，支持 hover:(bg-red text-white) 语法
} from 'unocss'
import { presetAnimations } from 'unocss-preset-animations' // 动画预设
import { darkTheme, lightTheme } from './themes' // 导入主题配置

export default defineConfig<Theme>({
  // 内容扫描配置 - 指定哪些文件需要被扫描以提取样式类
  content: {
    pipeline: {
      include: [
        // 扫描 Vue、Svelte、JSX、Markdown 等文件
        /\.(vue|svelte|[jt]sx|mdx?|astro|elm|php|phtml|html)($|\?)/,
        // 扫描 src 目录下的 JS/TS 文件
        'src/**/*.{js,ts}',
      ],
    },
  },

  // 快捷类名配置 - 定义自定义的快捷类名
  shortcuts: [
    // 定义 flex 布局的快捷类名
    // 支持 flex-col-center、flex-start-between 等组合写法
    [/^flex-?(col)?-(start|end|center|baseline|stretch)-?(start|end|center|between|around|evenly|left|right)?$/, ([, col, items, justify]) => {
      const cls = ['flex']

      // 如果包含 col，添加 flex-col 类
      if (col === 'col') {
        cls.push('flex-col')
      }

      // 如果 items 为 center 且没有 justify，同时设置水平和垂直居中
      if (items === 'center' && !justify) {
        cls.push('items-center')
        cls.push('justify-center')
      }
      else {
        // 添加 items 对齐方式
        cls.push(`items-${items}`)
        // 如果有 justify，添加 justify 对齐方式
        if (justify) {
          cls.push(`justify-${justify}`)
        }
      }
      return cls.join(' ')
    }],
  ],

  // 预设配置 - 定义使用的预设插件
  presets: [
    // Tailwind CSS v3 兼容预设，提供 Tailwind 样式
    presetWind3(),

    // 动画预设，提供动画相关的工具类
    presetAnimations(),

    // 属性化预设，支持在 HTML 属性中写样式
    // 例如：<div text="red center" bg="blue-500" />
    presetAttributify(),

    // 图标预设，支持各种图标库（如 Iconify）
    presetIcons({
      extraProperties: {
        'display': 'inline-block', // 图标默认为行内块元素
        'vertical-align': 'middle', // 图标垂直居中对齐
      },
    }),

    // 排版预设，提供排版相关的工具类
    presetTypography(),

    // 传统兼容预设，提供向后兼容性
    presetLegacyCompat({
      legacyColorSpace: true, // 启用传统颜色空间支持
    }),

    // 自定义 shadcn/ui 预设配置
    {
      name: 'unocss-preset-shadcn',
      preflights: [
        {
          // 生成全局 CSS 样式
          getCSS: () => {
            const returnCss: any = []

            // 生成明亮主题的 CSS 变量
            const lightCss = entriesToCss(Object.entries(lightTheme))
            returnCss.push(`:root{${lightCss}}`)

            // 生成暗黑主题的 CSS 变量
            const darkCss = entriesToCss(Object.entries(darkTheme))
            returnCss.push(`html.dark{${darkCss}}`)

            return `
${returnCss.join('\n')}

/* 全局样式重置 */
* {
  border-color: hsl(var(--border));
}

body {
  color: hsl(var(--foreground));
  background: hsl(var(--background));
}
`
          },
        },
      ],

      // 主题配置 - 定义设计系统的颜色和尺寸
      theme: {
        // 颜色配置 - 使用 CSS 变量实现主题切换
        colors: {
          border: 'hsl(var(--border))', // 边框颜色
          input: 'hsl(var(--input))', // 输入框颜色
          ring: 'hsl(var(--ring))', // 聚焦环颜色
          background: 'hsl(var(--background))', // 背景颜色
          foreground: 'hsl(var(--foreground))', // 前景颜色

          // 主要颜色
          primary: {
            DEFAULT: 'hsl(var(--primary))', // 主要颜色
            foreground: 'hsl(var(--primary-foreground))', // 主要颜色的前景色
          },

          // 次要颜色
          secondary: {
            DEFAULT: 'hsl(var(--secondary))', // 次要颜色
            foreground: 'hsl(var(--secondary-foreground))', // 次要颜色的前景色
          },

          // 破坏性/危险颜色
          destructive: {
            DEFAULT: 'hsl(var(--destructive))', // 危险颜色
            foreground: 'hsl(var(--destructive-foreground))', // 危险颜色的前景色
          },

          // 静音/禁用颜色
          muted: {
            DEFAULT: 'hsl(var(--muted))', // 静音颜色
            foreground: 'hsl(var(--muted-foreground))', // 静音颜色的前景色
          },

          // 强调颜色
          accent: {
            DEFAULT: 'hsl(var(--accent))', // 强调颜色
            foreground: 'hsl(var(--accent-foreground))', // 强调颜色的前景色
          },

          // 弹出框颜色
          popover: {
            DEFAULT: 'hsl(var(--popover))', // 弹出框颜色
            foreground: 'hsl(var(--popover-foreground))', // 弹出框前景色
          },

          // 卡片颜色
          card: {
            DEFAULT: 'hsl(var(--card))', // 卡片颜色
            foreground: 'hsl(var(--card-foreground))', // 卡片前景色
          },
        },

        // 圆角配置 - 基于 CSS 变量的响应式圆角
        borderRadius: {
          xl: 'calc(var(--radius) + 4px)', // 超大圆角
          lg: 'var(--radius)', // 大圆角
          md: 'calc(var(--radius) - 2px)', // 中等圆角
          sm: 'calc(var(--radius) - 4px)', // 小圆角
        },
      },
    },
  ],

  // 转换器配置 - 定义代码转换规则
  transformers: [
    transformerDirectives(), // 支持 @apply、@screen 等指令
    transformerVariantGroup(), // 支持变体组语法 hover:(bg-red text-white)
    transformerCompileClass(), // 编译类转换，将多个类合并为一个类
  ],

  // 配置依赖 - 当这些文件改变时重新加载配置
  configDeps: [
    'themes/index.ts', // 主题配置文件
  ],
})
